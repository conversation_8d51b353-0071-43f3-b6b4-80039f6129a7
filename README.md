# Deepfake Predictor

A Qt6/C++ desktop application for AI-powered deepfake detection in video and audio files.

## Features

- **Drag & Drop Interface**: Easy-to-use interface for loading media files
- **Multi-Modal Analysis**: Combined face and voice analysis for comprehensive detection
- **Real-time Processing**: OpenCV-based video frame extraction and analysis
- **Machine Learning Models**: ONNX Runtime integration for neural network inference
- **Visual Results**: Interactive thumbnails, waveforms, and timeline visualization
- **Report Generation**: Export analysis results as PDF reports or screenshots
- **Cross-Platform**: CMake build system for Windows, macOS, and Linux

## Screenshots

The application provides a modern, dark-themed interface with:
- Left panel for file input and controls
- Right panel showing analysis results with score gauges
- Visual analysis section with face thumbnails and audio waveforms
- Progress tracking and detailed logging

## Dependencies

### Required Libraries

1. **Qt6** (6.2 or later)
   - Core, Widgets, Quick, QuickWidgets, PrintSupport modules
   
2. **OpenCV** (4.5 or later)
   - Core computer vision functionality
   - Video processing and face detection
   
3. **ONNX Runtime** (1.12 or later) - Optional but recommended
   - Neural network inference engine
   - Enables advanced ML model support

### System Requirements

- **OS**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 20.04+)
- **Compiler**: C++17 compatible (GCC 9+, Clang 10+, MSVC 2019+)
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 500MB for application + models

## Installation

### Ubuntu/Debian

```bash
# Install Qt6
sudo apt update
sudo apt install qt6-base-dev qt6-tools-dev cmake build-essential

# Install OpenCV
sudo apt install libopencv-dev

# Install ONNX Runtime (optional)
wget https://github.com/microsoft/onnxruntime/releases/download/v1.16.3/onnxruntime-linux-x64-1.16.3.tgz
tar -xzf onnxruntime-linux-x64-1.16.3.tgz
sudo cp -r onnxruntime-linux-x64-1.16.3/include/* /usr/local/include/
sudo cp -r onnxruntime-linux-x64-1.16.3/lib/* /usr/local/lib/
sudo ldconfig
```

### macOS

```bash
# Install dependencies via Homebrew
brew install qt6 opencv cmake

# Install ONNX Runtime (optional)
brew install onnxruntime
```

### Windows

1. Install Qt6 from https://www.qt.io/download
2. Install OpenCV from https://opencv.org/releases/
3. Download ONNX Runtime from https://github.com/microsoft/onnxruntime/releases
4. Install Visual Studio 2019 or later with C++ support

## Building

### Clone and Build

```bash
git clone <repository-url>
cd "Deepfake predictor"

# Create build directory
mkdir build
cd build

# Configure with CMake
cmake ..

# Build the application
make -j$(nproc)  # Linux/macOS
# or
cmake --build . --config Release  # Windows
```

### CMake Options

```bash
# Build with specific Qt6 path
cmake -DCMAKE_PREFIX_PATH=/path/to/qt6 ..

# Build without ONNX Runtime
cmake -DHAVE_ONNXRUNTIME=OFF ..

# Debug build
cmake -DCMAKE_BUILD_TYPE=Debug ..
```

## Model Setup

The application requires pre-trained ONNX models for optimal performance:

### Download Models

Create a `models` directory and download the following models:

```bash
mkdir models
cd models

# Face detection model (example URLs - replace with actual model sources)
wget https://github.com/onnx/models/raw/main/vision/body_analysis/emotion_ferplus/model/emotion-ferplus-8.onnx -O face-detector.onnx

# Voice analysis model (example - replace with actual model)
wget https://github.com/onnx/models/raw/main/text/machine_comprehension/gpt-2/model/gpt2-10.onnx -O voice-detector.onnx

# OpenCV Haar cascade (if not system-installed)
wget https://raw.githubusercontent.com/opencv/opencv/master/data/haarcascades/haarcascade_frontalface_alt.xml
```

### Model Requirements

- **Face Model**: Input shape [1, 3, 224, 224], output single float (0-1 score)
- **Voice Model**: Input shape [1, N] where N is feature count, output single float (0-1 score)
- **Format**: ONNX format (.onnx files)

**Note**: The example URLs above are placeholders. You'll need to:
1. Train your own models, or
2. Find suitable pre-trained models for deepfake detection
3. Ensure models match the expected input/output format

## Usage

### GUI Mode

```bash
# Run the application
./DeepfakePredictor

# Or with a file argument
./DeepfakePredictor path/to/video.mp4
```

### Command Line (Future Feature)

```bash
# Batch processing (not yet implemented)
./DeepfakePredictor --batch input.mp4 --output report.pdf
```

### Supported File Formats

- **Video**: MP4, AVI, MOV, MKV, WMV, FLV, WebM
- **Audio**: MP3, WAV, FLAC, AAC, OGG, M4A

### Workflow

1. **Load File**: Drag & drop or use "Open File" button
2. **Analyze**: Click "Analyze for Deepfakes" button
3. **Review Results**: Check score gauges and visual analysis
4. **Save Report**: Click "Save Report" to export PDF or screenshot

## Results Interpretation

### Deepfake Scores

- **0-30%**: Low likelihood of synthetic content (likely authentic)
- **30-70%**: Moderate likelihood (requires further investigation)
- **70-100%**: High likelihood of deepfake/synthetic content

### Confidence Levels

- **80-100%**: High confidence in analysis
- **60-79%**: Medium confidence
- **40-59%**: Low confidence
- **0-39%**: Very low confidence (results may be unreliable)

## Development

### Project Structure

```
src/
├── main.cpp              # Application entry point
├── appwindow.h/cpp       # Main window and UI
├── deepfakedetector.h/cpp # Core detection engine
├── visualizer.h/cpp      # Results visualization
└── reportgenerator.h/cpp # PDF/image report generation

models/                   # ONNX model files
CMakeLists.txt           # Build configuration
README.md               # This file
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Troubleshooting

### Common Issues

**Models not found**: Ensure models are in the `models/` directory relative to the executable.

**ONNX Runtime errors**: Install ONNX Runtime or build without it using `-DHAVE_ONNXRUNTIME=OFF`.

**Qt6 not found**: Set `CMAKE_PREFIX_PATH` to your Qt6 installation directory.

**OpenCV errors**: Install OpenCV development packages for your system.

### Performance Tips

- Use SSD storage for better video processing performance
- Ensure adequate RAM (8GB+) for large video files
- Close other applications during analysis for optimal performance

## License

This project is provided as-is for educational and research purposes. Please ensure compliance with applicable laws and regulations when using deepfake detection technology.

## Disclaimer

This tool is for informational purposes only. Results should be verified by human experts for critical applications. The accuracy of detection depends on the quality of the trained models and input media.
