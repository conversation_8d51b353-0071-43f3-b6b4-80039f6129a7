#ifndef REPORTGENERATOR_H
#define REPORTGENERATOR_H

#include <QObject>
#include <QString>
#include <QPixmap>
#include <QPainter>
#include <QPdfWriter>
#include <QPagedPaintDevice>
#include <QTextDocument>
#include <QTextCursor>
#include <QTextTable>
#include <QTextTableFormat>
#include <QTextCharFormat>
#include <QTextBlockFormat>
#include <QDateTime>
#include <QFileInfo>
#include <QDir>
#include <QApplication>
#include <QScreen>
#include <QWidget>

#include "deepfakedetector.h"

class ReportGenerator : public QObject
{
    Q_OBJECT

public:
    explicit ReportGenerator(QObject *parent = nullptr);
    
    bool generateReport(const DetectionResult &result, const QString &outputPath);
    bool generateScreenshot(QWidget *widget, const QString &outputPath);

private:
    // PDF generation methods
    bool generatePdfReport(const DetectionResult &result, const QString &outputPath);
    void addReportHeader(QTextCursor &cursor, const DetectionResult &result);
    void addExecutiveSummary(QTextCursor &cursor, const DetectionResult &result);
    void addDetailedAnalysis(QTextCursor &cursor, const DetectionResult &result);
    void addFaceAnalysisSection(QTextCursor &cursor, const DetectionResult &result);
    void addAudioAnalysisSection(QTextCursor &cursor, const DetectionResult &result);
    void addTechnicalDetails(QTextCursor &cursor, const DetectionResult &result);
    void addFooter(QTextCursor &cursor);
    
    // Image generation methods
    bool generateImageReport(const DetectionResult &result, const QString &outputPath);
    QPixmap createReportImage(const DetectionResult &result);
    QPixmap createScoreVisualization(const DetectionResult &result);
    QPixmap createFaceThumbnailGrid(const QVector<FaceDetection> &detections);
    QPixmap createAudioWaveform(const QVector<float> &waveform, 
                               const QVector<AudioSegment> &segments);
    QPixmap createTimeline(const QVector<bool> &suspiciousFrames);
    
    // Utility methods
    QString getFileExtension(const QString &filePath);
    QString formatDuration(float seconds);
    QString getConfidenceLevel(float confidence);
    QString getRiskLevel(float score);
    QColor getScoreColor(float score);
    void setupTextFormats();
    
    // Text formatting
    QTextCharFormat m_headerFormat;
    QTextCharFormat m_subHeaderFormat;
    QTextCharFormat m_normalFormat;
    QTextCharFormat m_boldFormat;
    QTextCharFormat m_italicFormat;
    QTextCharFormat m_codeFormat;
    
    QTextBlockFormat m_centerFormat;
    QTextBlockFormat m_leftFormat;
    
    // Constants
    static const int REPORT_WIDTH = 800;
    static const int REPORT_MARGIN = 50;
    static const int THUMBNAIL_SIZE = 48;
    static const int WAVEFORM_HEIGHT = 60;
    static const int TIMELINE_HEIGHT = 20;
};

#endif // REPORTGENERATOR_H
