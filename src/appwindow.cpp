#include "appwindow.h"
#include <QApplication>
#include <QMessageBox>
#include <QFileInfo>
#include <QMimeDatabase>
#include <QPainter>
#include <QFont>
#include <QFontMetrics>

// DropArea Implementation
DropArea::DropArea(QWidget *parent)
    : QFrame(parent), m_dragActive(false)
{
    setAcceptDrops(true);
    setFrameStyle(QFrame::StyledPanel | QFrame::Sunken);
    setLineWidth(2);
    setMinimumHeight(150);
    
    m_supportedFormats = "Supported formats: MP4, AVI, MOV, MKV, MP3, WAV, FLAC";
}

void DropArea::dragEnterEvent(QDragEnterEvent *event)
{
    if (event->mimeData()->hasUrls()) {
        QList<QUrl> urls = event->mimeData()->urls();
        if (!urls.isEmpty()) {
            QString fileName = urls.first().toLocalFile();
            QFileInfo fileInfo(fileName);
            QString suffix = fileInfo.suffix().toLower();
            
            // Check if file format is supported
            QStringList supportedFormats = {"mp4", "avi", "mov", "mkv", "mp3", "wav", "flac", "m4a"};
            if (supportedFormats.contains(suffix)) {
                event->acceptProposedAction();
                m_dragActive = true;
                update();
                return;
            }
        }
    }
    event->ignore();
}

void DropArea::dragMoveEvent(QDragMoveEvent *event)
{
    event->acceptProposedAction();
}

void DropArea::dropEvent(QDropEvent *event)
{
    if (event->mimeData()->hasUrls()) {
        QList<QUrl> urls = event->mimeData()->urls();
        if (!urls.isEmpty()) {
            QString filePath = urls.first().toLocalFile();
            emit fileDropped(filePath);
        }
    }
    m_dragActive = false;
    update();
    event->acceptProposedAction();
}

void DropArea::paintEvent(QPaintEvent *event)
{
    QFrame::paintEvent(event);
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    QRect rect = this->rect().adjusted(10, 10, -10, -10);
    
    if (m_dragActive) {
        painter.setPen(QPen(QColor(42, 130, 218), 3, Qt::DashLine));
        painter.setBrush(QColor(42, 130, 218, 30));
    } else {
        painter.setPen(QPen(QColor(100, 100, 100), 2, Qt::DashLine));
        painter.setBrush(QColor(70, 70, 70, 50));
    }
    
    painter.drawRoundedRect(rect, 10, 10);
    
    // Draw text
    painter.setPen(Qt::white);
    QFont font = painter.font();
    font.setPointSize(12);
    painter.setFont(font);
    
    QString mainText = "Drop video or audio file here";
    QString subText = m_supportedFormats;
    
    QFontMetrics fm(font);
    QRect mainTextRect = fm.boundingRect(mainText);
    
    font.setPointSize(10);
    painter.setFont(font);
    QFontMetrics fm2(font);
    QRect subTextRect = fm2.boundingRect(subText);
    
    int totalHeight = mainTextRect.height() + subTextRect.height() + 10;
    int startY = rect.center().y() - totalHeight / 2;
    
    font.setPointSize(12);
    painter.setFont(font);
    QRect mainTextBounds(rect.x(), startY, rect.width(), mainTextRect.height());
    painter.drawText(mainTextBounds, Qt::AlignHCenter | Qt::AlignTop, mainText);

    font.setPointSize(10);
    painter.setFont(font);
    painter.setPen(QColor(180, 180, 180));
    QRect subTextBounds(rect.x(), startY + mainTextRect.height() + 10,
                       rect.width(), subTextRect.height());
    painter.drawText(subTextBounds, Qt::AlignHCenter | Qt::AlignTop, subText);
}

// AppWindow Implementation
AppWindow::AppWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_detector(nullptr)
    , m_reportGenerator(nullptr)
    , m_detectorThread(nullptr)
    , m_isAnalyzing(false)
{
    setWindowTitle("Deepfake Predictor v1.0");
    setMinimumSize(WINDOW_MIN_WIDTH, WINDOW_MIN_HEIGHT);
    resize(1400, 900);
    
    setupMenuBar();
    setupUI();
    setupStatusBar();
    
    // Initialize components
    m_detector = new DeepfakeDetector(this);
    m_reportGenerator = new ReportGenerator(this);
    
    // Connect signals
    connect(m_detector, &DeepfakeDetector::progressUpdated, 
            this, &AppWindow::onDetectionProgress);
    connect(m_detector, &DeepfakeDetector::detectionComplete, 
            this, &AppWindow::onDetectionComplete);
    connect(m_detector, &DeepfakeDetector::detectionError, 
            this, &AppWindow::onDetectionError);
}

AppWindow::~AppWindow()
{
    if (m_detectorThread && m_detectorThread->isRunning()) {
        m_detectorThread->quit();
        m_detectorThread->wait();
    }
}

void AppWindow::setupUI()
{
    m_centralWidget = new QWidget;
    setCentralWidget(m_centralWidget);
    
    // Main splitter
    m_mainSplitter = new QSplitter(Qt::Horizontal);
    
    // Left panel setup
    QWidget *leftPanel = new QWidget;
    leftPanel->setMaximumWidth(LEFT_PANEL_WIDTH);
    leftPanel->setMinimumWidth(LEFT_PANEL_WIDTH);
    
    QVBoxLayout *leftLayout = new QVBoxLayout(leftPanel);
    
    // Input group
    m_inputGroup = new QGroupBox("Input File");
    QVBoxLayout *inputLayout = new QVBoxLayout(m_inputGroup);
    
    m_dropArea = new DropArea;
    connect(m_dropArea, &DropArea::fileDropped, this, &AppWindow::onFileDropped);
    
    m_openButton = new QPushButton("Open File...");
    connect(m_openButton, &QPushButton::clicked, this, &AppWindow::onOpenFile);
    
    m_fileInfoLabel = new QLabel("No file selected");
    m_fileInfoLabel->setWordWrap(true);
    m_fileInfoLabel->setStyleSheet("QLabel { color: #B0B0B0; font-size: 11px; }");
    
    inputLayout->addWidget(m_dropArea);
    inputLayout->addWidget(m_openButton);
    inputLayout->addWidget(m_fileInfoLabel);
    
    // Control buttons
    m_analyzeButton = new QPushButton("Analyze for Deepfakes");
    m_analyzeButton->setEnabled(false);
    m_analyzeButton->setStyleSheet("QPushButton { font-weight: bold; padding: 8px; }");
    connect(m_analyzeButton, &QPushButton::clicked, this, &AppWindow::onAnalyzeFile);
    
    m_saveReportButton = new QPushButton("Save Report");
    m_saveReportButton->setEnabled(false);
    connect(m_saveReportButton, &QPushButton::clicked, this, &AppWindow::onSaveReport);
    
    leftLayout->addWidget(m_inputGroup);
    leftLayout->addWidget(m_analyzeButton);
    leftLayout->addWidget(m_saveReportButton);
    leftLayout->addStretch();
    
    m_mainSplitter->addWidget(leftPanel);
    
    // Right panel setup
    QWidget *rightPanel = new QWidget;
    m_rightSplitter = new QSplitter(Qt::Vertical);

    // Results group
    m_resultsGroup = new QGroupBox("Detection Results");
    QVBoxLayout *resultsLayout = new QVBoxLayout(m_resultsGroup);

    m_scoreLabel = new QLabel("Deepfake Score: --");
    m_scoreLabel->setStyleSheet("QLabel { font-size: 16px; font-weight: bold; }");

    m_scoreBar = new QProgressBar;
    m_scoreBar->setRange(0, 100);
    m_scoreBar->setValue(0);
    m_scoreBar->setTextVisible(true);
    m_scoreBar->setFormat("%p% Synthetic");

    m_confidenceLabel = new QLabel("Confidence: --");

    resultsLayout->addWidget(m_scoreLabel);
    resultsLayout->addWidget(m_scoreBar);
    resultsLayout->addWidget(m_confidenceLabel);

    // Progress group
    m_progressGroup = new QGroupBox("Analysis Progress");
    QVBoxLayout *progressLayout = new QVBoxLayout(m_progressGroup);

    m_analysisProgress = new QProgressBar;
    m_analysisProgress->setRange(0, 100);
    m_analysisProgress->setValue(0);

    m_progressLabel = new QLabel("Ready");

    m_logOutput = new QTextEdit;
    m_logOutput->setMaximumHeight(100);
    m_logOutput->setReadOnly(true);
    m_logOutput->setStyleSheet("QTextEdit { font-family: monospace; font-size: 10px; }");

    progressLayout->addWidget(m_progressLabel);
    progressLayout->addWidget(m_analysisProgress);
    progressLayout->addWidget(m_logOutput);

    // Visualization group
    m_visualGroup = new QGroupBox("Visual Analysis");
    QVBoxLayout *visualLayout = new QVBoxLayout(m_visualGroup);

    m_visualizer = new Visualizer;
    visualLayout->addWidget(m_visualizer);

    m_rightSplitter->addWidget(m_resultsGroup);
    m_rightSplitter->addWidget(m_progressGroup);
    m_rightSplitter->addWidget(m_visualGroup);

    // Set splitter proportions
    m_rightSplitter->setSizes({150, 200, 400});

    rightPanel->setLayout(new QVBoxLayout);
    rightPanel->layout()->addWidget(m_rightSplitter);

    m_mainSplitter->addWidget(rightPanel);
    m_mainSplitter->setSizes({LEFT_PANEL_WIDTH, 800});

    // Main layout
    QHBoxLayout *mainLayout = new QHBoxLayout(m_centralWidget);
    mainLayout->addWidget(m_mainSplitter);
    mainLayout->setContentsMargins(5, 5, 5, 5);
}

void AppWindow::setupMenuBar()
{
    QMenuBar *menuBar = this->menuBar();

    // File menu
    QMenu *fileMenu = menuBar->addMenu("&File");

    QAction *openAction = new QAction("&Open...", this);
    openAction->setShortcut(QKeySequence::Open);
    connect(openAction, &QAction::triggered, this, &AppWindow::onOpenFile);
    fileMenu->addAction(openAction);

    fileMenu->addSeparator();

    QAction *exitAction = new QAction("E&xit", this);
    exitAction->setShortcut(QKeySequence::Quit);
    connect(exitAction, &QAction::triggered, this, &QWidget::close);
    fileMenu->addAction(exitAction);

    // Tools menu
    QMenu *toolsMenu = menuBar->addMenu("&Tools");

    QAction *analyzeAction = new QAction("&Analyze File", this);
    analyzeAction->setShortcut(QKeySequence("Ctrl+A"));
    connect(analyzeAction, &QAction::triggered, this, &AppWindow::onAnalyzeFile);
    toolsMenu->addAction(analyzeAction);

    QAction *saveReportAction = new QAction("&Save Report", this);
    saveReportAction->setShortcut(QKeySequence::Save);
    connect(saveReportAction, &QAction::triggered, this, &AppWindow::onSaveReport);
    toolsMenu->addAction(saveReportAction);

    // Help menu
    QMenu *helpMenu = menuBar->addMenu("&Help");

    QAction *aboutAction = new QAction("&About", this);
    connect(aboutAction, &QAction::triggered, this, &AppWindow::onAbout);
    helpMenu->addAction(aboutAction);
}

void AppWindow::setupStatusBar()
{
    statusBar()->showMessage("Ready - Drop a video or audio file to begin analysis");
}

void AppWindow::loadFile(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists()) {
        QMessageBox::warning(this, "File Not Found",
                           "The specified file does not exist: " + filePath);
        return;
    }

    m_currentFile = filePath;
    m_fileInfoLabel->setText(QString("File: %1\nSize: %2\nType: %3")
                            .arg(fileInfo.fileName())
                            .arg(QString::number(fileInfo.size() / 1024.0 / 1024.0, 'f', 1) + " MB")
                            .arg(fileInfo.suffix().toUpper()));

    m_analyzeButton->setEnabled(true);
    resetUI();

    statusBar()->showMessage("File loaded: " + fileInfo.fileName());
}

void AppWindow::onFileDropped(const QString &filePath)
{
    loadFile(filePath);
}

void AppWindow::onOpenFile()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        "Open Media File", "",
        "Media Files (*.mp4 *.avi *.mov *.mkv *.mp3 *.wav *.flac *.m4a);;All Files (*)");

    if (!fileName.isEmpty()) {
        loadFile(fileName);
    }
}

void AppWindow::onAnalyzeFile()
{
    if (m_currentFile.isEmpty()) {
        QMessageBox::warning(this, "No File", "Please select a file to analyze first.");
        return;
    }

    if (m_isAnalyzing) {
        QMessageBox::information(this, "Analysis in Progress",
                               "Analysis is already in progress. Please wait for it to complete.");
        return;
    }

    setAnalyzing(true);
    resetUI();

    m_logOutput->append("Starting deepfake analysis...");
    m_logOutput->append("File: " + QFileInfo(m_currentFile).fileName());

    // Start detection in separate thread
    if (!m_detectorThread) {
        m_detectorThread = new QThread(this);
        m_detector->moveToThread(m_detectorThread);
        m_detectorThread->start();
    }

    // Start analysis
    QMetaObject::invokeMethod(m_detector, "analyzeFile",
                             Qt::QueuedConnection,
                             Q_ARG(QString, m_currentFile));
}

void AppWindow::onSaveReport()
{
    if (m_lastResult.filePath.isEmpty()) {
        QMessageBox::warning(this, "No Results",
                           "No analysis results available. Please analyze a file first.");
        return;
    }

    QString fileName = QFileDialog::getSaveFileName(this,
        "Save Analysis Report",
        QFileInfo(m_lastResult.filePath).baseName() + "_deepfake_report.pdf",
        "PDF Files (*.pdf);;PNG Images (*.png);;All Files (*)");

    if (!fileName.isEmpty()) {
        if (m_reportGenerator->generateReport(m_lastResult, fileName)) {
            QMessageBox::information(this, "Report Saved",
                                   "Analysis report saved successfully to:\n" + fileName);
            statusBar()->showMessage("Report saved: " + QFileInfo(fileName).fileName());
        } else {
            QMessageBox::critical(this, "Save Error",
                                "Failed to save the analysis report.");
        }
    }
}

void AppWindow::onAbout()
{
    QMessageBox::about(this, "About Deepfake Predictor",
        "<h3>Deepfake Predictor v1.0</h3>"
        "<p>AI-powered deepfake detection tool for video and audio files.</p>"
        "<p>Features:</p>"
        "<ul>"
        "<li>Video frame analysis using computer vision</li>"
        "<li>Audio analysis using machine learning models</li>"
        "<li>Combined scoring for comprehensive detection</li>"
        "<li>Visual analysis and reporting</li>"
        "</ul>"
        "<p>Built with Qt6, OpenCV, and ONNX Runtime.</p>");
}

void AppWindow::onDetectionProgress(int percentage)
{
    m_analysisProgress->setValue(percentage);
    m_progressLabel->setText(QString("Analyzing... %1%").arg(percentage));

    if (percentage == 25) {
        m_logOutput->append("✓ Video frames extracted");
    } else if (percentage == 50) {
        m_logOutput->append("✓ Audio track processed");
    } else if (percentage == 75) {
        m_logOutput->append("✓ Face analysis complete");
    } else if (percentage == 90) {
        m_logOutput->append("✓ Voice analysis complete");
    }
}

void AppWindow::onDetectionComplete(const DetectionResult &result)
{
    setAnalyzing(false);
    m_lastResult = result;

    m_analysisProgress->setValue(100);
    m_progressLabel->setText("Analysis Complete");
    m_logOutput->append("✓ Analysis completed successfully");

    updateUI(result);

    statusBar()->showMessage(QString("Analysis complete - Deepfake score: %1%")
                           .arg(result.overallScore, 0, 'f', 1));
}

void AppWindow::onDetectionError(const QString &error)
{
    setAnalyzing(false);

    m_progressLabel->setText("Analysis Failed");
    m_logOutput->append("✗ Error: " + error);

    QMessageBox::critical(this, "Analysis Error",
                         "Failed to analyze the file:\n" + error);

    statusBar()->showMessage("Analysis failed");
}

void AppWindow::resetUI()
{
    m_scoreLabel->setText("Deepfake Score: --");
    m_scoreBar->setValue(0);
    m_confidenceLabel->setText("Confidence: --");
    m_analysisProgress->setValue(0);
    m_progressLabel->setText("Ready");
    m_visualizer->clear();
    m_saveReportButton->setEnabled(false);
}

void AppWindow::updateUI(const DetectionResult &result)
{
    // Update score display
    m_scoreLabel->setText(QString("Deepfake Score: %1%").arg(result.overallScore, 0, 'f', 1));
    m_scoreBar->setValue(static_cast<int>(result.overallScore));

    // Color code the progress bar based on score
    QString styleSheet;
    if (result.overallScore < 30) {
        styleSheet = "QProgressBar::chunk { background-color: #4CAF50; }"; // Green
    } else if (result.overallScore < 70) {
        styleSheet = "QProgressBar::chunk { background-color: #FF9800; }"; // Orange
    } else {
        styleSheet = "QProgressBar::chunk { background-color: #F44336; }"; // Red
    }
    m_scoreBar->setStyleSheet(styleSheet);

    m_confidenceLabel->setText(QString("Confidence: %1%").arg(result.confidence, 0, 'f', 1));

    // Update visualizer
    m_visualizer->setResult(result);

    // Enable save report button
    m_saveReportButton->setEnabled(true);
}

void AppWindow::setAnalyzing(bool analyzing)
{
    m_isAnalyzing = analyzing;
    m_analyzeButton->setEnabled(!analyzing && !m_currentFile.isEmpty());
    m_openButton->setEnabled(!analyzing);
    m_dropArea->setEnabled(!analyzing);

    if (analyzing) {
        m_logOutput->clear();
        statusBar()->showMessage("Analyzing file...");
    }
}
