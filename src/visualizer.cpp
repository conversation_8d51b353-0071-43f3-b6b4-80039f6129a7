#include "visualizer.h"
#include <QApplication>
#include <QPainterPath>
#include <QLinearGradient>
#include <QRadialGradient>
#include <QFontMetrics>
#include <QToolTip>
#include <QFileInfo>
#include <cmath>

// ScoreGauge Implementation
ScoreGauge::ScoreGauge(QWidget *parent)
    : QWidget(parent)
    , m_value(0.0f)
    , m_targetValue(0.0f)
    , m_title("Score")
    , m_animated(true)
    , m_animation(nullptr)
{
    setFixedSize(GAUGE_SIZE, GAUGE_SIZE);
    
    m_animation = new QPropertyAnimation(this, "value");
    m_animation->setDuration(1000);
    m_animation->setEasingCurve(QEasingCurve::OutCubic);
}

void ScoreGauge::setValue(float value)
{
    m_targetValue = qBound(0.0f, value, 100.0f);
    
    if (m_animated && m_animation) {
        m_animation->stop();
        m_animation->setStartValue(m_value);
        m_animation->setEndValue(m_targetValue);
        m_animation->start();
    } else {
        m_value = m_targetValue;
        update();
    }
}

void ScoreGauge::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    QRect rect = this->rect().adjusted(10, 10, -10, -10);
    QPointF center = rect.center();
    int radius = qMin(rect.width(), rect.height()) / 2 - 5;
    
    // Background circle
    painter.setPen(QPen(QColor(60, 60, 60), 8));
    painter.drawEllipse(center, radius, radius);
    
    // Progress arc
    float angle = (m_value / 100.0f) * 270.0f; // 270 degrees max
    
    QColor progressColor;
    if (m_value < 30) {
        progressColor = QColor(76, 175, 80); // Green
    } else if (m_value < 70) {
        progressColor = QColor(255, 152, 0); // Orange
    } else {
        progressColor = QColor(244, 67, 54); // Red
    }
    
    painter.setPen(QPen(progressColor, 8, Qt::SolidLine, Qt::RoundCap));
    painter.drawArc(QRect(center.x() - radius, center.y() - radius, 
                         radius * 2, radius * 2), 
                   135 * 16, -angle * 16); // Start from bottom-left
    
    // Center text
    painter.setPen(Qt::white);
    QFont font = painter.font();
    font.setPointSize(14);
    font.setBold(true);
    painter.setFont(font);
    
    QString valueText = QString::number(m_value, 'f', 1) + "%";
    QFontMetrics fm(font);
    QRect textRect = fm.boundingRect(valueText);
    painter.drawText(center.x() - textRect.width() / 2, 
                    center.y() + textRect.height() / 4, valueText);
    
    // Title
    font.setPointSize(10);
    font.setBold(false);
    painter.setFont(font);
    painter.setPen(QColor(180, 180, 180));
    
    QFontMetrics fm2(font);
    QRect titleRect = fm2.boundingRect(m_title);
    painter.drawText(center.x() - titleRect.width() / 2,
                    center.y() + radius - 5, m_title);
}

QSize ScoreGauge::sizeHint() const
{
    return QSize(GAUGE_SIZE, GAUGE_SIZE);
}

// WaveformWidget Implementation
WaveformWidget::WaveformWidget(QWidget *parent)
    : QWidget(parent)
    , m_hoveredSegment(-1)
{
    setMinimumHeight(WAVEFORM_HEIGHT);
    setMouseTracking(true);
}

void WaveformWidget::setWaveform(const QVector<float> &waveform)
{
    m_waveform = waveform;
    update();
}

void WaveformWidget::setSuspiciousSegments(const QVector<AudioSegment> &segments)
{
    m_suspiciousSegments = segments;
    update();
}

void WaveformWidget::clear()
{
    m_waveform.clear();
    m_suspiciousSegments.clear();
    m_hoveredSegment = -1;
    update();
}

void WaveformWidget::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    QRect rect = this->rect().adjusted(5, 5, -5, -5);
    
    // Background
    painter.fillRect(rect, QColor(30, 30, 30));
    painter.setPen(QColor(100, 100, 100));
    painter.drawRect(rect);
    
    if (m_waveform.isEmpty()) {
        painter.setPen(Qt::gray);
        painter.drawText(rect, Qt::AlignCenter, "No audio data");
        return;
    }
    
    // Draw waveform
    painter.setPen(QColor(100, 200, 255));
    
    int centerY = rect.center().y();
    float xStep = static_cast<float>(rect.width()) / m_waveform.size();
    
    for (int i = 1; i < m_waveform.size(); i++) {
        float x1 = rect.left() + (i - 1) * xStep;
        float x2 = rect.left() + i * xStep;
        float y1 = centerY - m_waveform[i - 1] * rect.height() / 4;
        float y2 = centerY - m_waveform[i] * rect.height() / 4;
        
        painter.drawLine(QPointF(x1, y1), QPointF(x2, y2));
    }
    
    // Highlight suspicious segments
    for (int i = 0; i < m_suspiciousSegments.size(); i++) {
        const AudioSegment &segment = m_suspiciousSegments[i];
        
        if (segment.deepfakeScore > 0.5f) {
            float startX = rect.left() + (segment.startTime / 10.0f) * rect.width(); // Assuming 10s duration
            float endX = rect.left() + (segment.endTime / 10.0f) * rect.width();
            
            QColor highlightColor = (i == m_hoveredSegment) ? 
                                   QColor(255, 100, 100, 150) : 
                                   QColor(255, 0, 0, 100);
            
            painter.fillRect(QRectF(startX, rect.top(), endX - startX, rect.height()), 
                           highlightColor);
        }
    }
}

void WaveformWidget::mousePressEvent(QMouseEvent *event)
{
    if (m_suspiciousSegments.isEmpty()) return;
    
    QRect rect = this->rect().adjusted(5, 5, -5, -5);
    float clickX = event->pos().x() - rect.left();
    float timeRatio = clickX / rect.width();
    float clickTime = timeRatio * 10.0f; // Assuming 10s duration
    
    // Find clicked segment
    for (int i = 0; i < m_suspiciousSegments.size(); i++) {
        const AudioSegment &segment = m_suspiciousSegments[i];
        if (clickTime >= segment.startTime && clickTime <= segment.endTime) {
            emit segmentClicked(i);
            break;
        }
    }
}

QSize WaveformWidget::sizeHint() const
{
    return QSize(400, WAVEFORM_HEIGHT);
}

// ThumbnailGrid Implementation
ThumbnailGrid::ThumbnailGrid(QWidget *parent)
    : QWidget(parent)
    , m_hoveredThumbnail(-1)
    , m_columns(4)
{
    setMouseTracking(true);
}

void ThumbnailGrid::setFaceDetections(const QVector<FaceDetection> &detections)
{
    m_detections = detections;
    updateLayout();
    update();
}

void ThumbnailGrid::clear()
{
    m_detections.clear();
    m_thumbnailRects.clear();
    m_hoveredThumbnail = -1;
    update();
}

void ThumbnailGrid::updateLayout()
{
    m_thumbnailRects.clear();
    
    if (m_detections.isEmpty()) {
        setFixedHeight(50);
        return;
    }
    
    int rows = (m_detections.size() + m_columns - 1) / m_columns;
    int totalHeight = rows * (THUMBNAIL_SIZE + THUMBNAIL_SPACING) + THUMBNAIL_SPACING;
    setFixedHeight(totalHeight);
    
    for (int i = 0; i < m_detections.size(); i++) {
        int row = i / m_columns;
        int col = i % m_columns;
        
        int x = col * (THUMBNAIL_SIZE + THUMBNAIL_SPACING) + THUMBNAIL_SPACING;
        int y = row * (THUMBNAIL_SIZE + THUMBNAIL_SPACING) + THUMBNAIL_SPACING;
        
        m_thumbnailRects.append(QRect(x, y, THUMBNAIL_SIZE, THUMBNAIL_SIZE));
    }
}

void ThumbnailGrid::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    if (m_detections.isEmpty()) {
        painter.setPen(Qt::gray);
        painter.drawText(rect(), Qt::AlignCenter, "No face detections");
        return;
    }

    for (int i = 0; i < m_detections.size() && i < m_thumbnailRects.size(); i++) {
        const FaceDetection &detection = m_detections[i];
        const QRect &thumbRect = m_thumbnailRects[i];

        // Draw thumbnail
        if (!detection.thumbnail.isNull()) {
            painter.drawPixmap(thumbRect, detection.thumbnail.scaled(
                THUMBNAIL_SIZE, THUMBNAIL_SIZE, Qt::KeepAspectRatio, Qt::SmoothTransformation));
        }

        // Draw border based on deepfake score
        QColor borderColor;
        if (detection.deepfakeScore < 0.3f) {
            borderColor = QColor(76, 175, 80); // Green
        } else if (detection.deepfakeScore < 0.7f) {
            borderColor = QColor(255, 152, 0); // Orange
        } else {
            borderColor = QColor(244, 67, 54); // Red
        }

        int borderWidth = (i == m_hoveredThumbnail) ? 3 : 2;
        painter.setPen(QPen(borderColor, borderWidth));
        painter.drawRect(thumbRect);

        // Draw score overlay
        painter.fillRect(thumbRect.left(), thumbRect.bottom() - 15,
                        thumbRect.width(), 15, QColor(0, 0, 0, 150));

        painter.setPen(Qt::white);
        QFont font = painter.font();
        font.setPointSize(8);
        painter.setFont(font);

        QString scoreText = QString::number(detection.deepfakeScore * 100, 'f', 0) + "%";
        painter.drawText(thumbRect.adjusted(2, 0, -2, -2), Qt::AlignBottom | Qt::AlignLeft, scoreText);
    }
}

void ThumbnailGrid::mousePressEvent(QMouseEvent *event)
{
    for (int i = 0; i < m_thumbnailRects.size(); i++) {
        if (m_thumbnailRects[i].contains(event->pos())) {
            emit thumbnailClicked(i);
            break;
        }
    }
}

QSize ThumbnailGrid::sizeHint() const
{
    if (m_detections.isEmpty()) {
        return QSize(400, 50);
    }

    int rows = (m_detections.size() + m_columns - 1) / m_columns;
    int totalHeight = rows * (THUMBNAIL_SIZE + THUMBNAIL_SPACING) + THUMBNAIL_SPACING;
    int totalWidth = m_columns * (THUMBNAIL_SIZE + THUMBNAIL_SPACING) + THUMBNAIL_SPACING;

    return QSize(totalWidth, totalHeight);
}

// TimelineWidget Implementation
TimelineWidget::TimelineWidget(QWidget *parent)
    : QWidget(parent)
    , m_duration(0.0f)
    , m_hoveredTime(-1.0f)
{
    setFixedHeight(TIMELINE_HEIGHT);
    setMouseTracking(true);
}

void TimelineWidget::setSuspiciousFrames(const QVector<bool> &suspiciousFrames)
{
    m_suspiciousFrames = suspiciousFrames;
    update();
}

void TimelineWidget::setDuration(float duration)
{
    m_duration = duration;
    update();
}

void TimelineWidget::clear()
{
    m_suspiciousFrames.clear();
    m_duration = 0.0f;
    m_hoveredTime = -1.0f;
    update();
}

void TimelineWidget::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    QRect rect = this->rect().adjusted(5, 5, -5, -5);

    // Background
    painter.fillRect(rect, QColor(40, 40, 40));
    painter.setPen(QColor(100, 100, 100));
    painter.drawRect(rect);

    if (m_suspiciousFrames.isEmpty()) {
        painter.setPen(Qt::gray);
        painter.drawText(rect, Qt::AlignCenter, "No timeline data");
        return;
    }

    // Draw timeline segments
    float segmentWidth = static_cast<float>(rect.width()) / m_suspiciousFrames.size();

    for (int i = 0; i < m_suspiciousFrames.size(); i++) {
        float x = rect.left() + i * segmentWidth;
        QRectF segmentRect(x, rect.top(), segmentWidth, rect.height());

        QColor segmentColor = m_suspiciousFrames[i] ?
                             QColor(244, 67, 54, 180) :  // Red for suspicious
                             QColor(76, 175, 80, 100);   // Green for normal

        painter.fillRect(segmentRect, segmentColor);
    }

    // Draw hover indicator
    if (m_hoveredTime >= 0 && m_duration > 0) {
        float hoverX = rect.left() + (m_hoveredTime / m_duration) * rect.width();
        painter.setPen(QPen(Qt::white, 2));
        painter.drawLine(hoverX, rect.top(), hoverX, rect.bottom());
    }
}

void TimelineWidget::mousePressEvent(QMouseEvent *event)
{
    if (m_duration <= 0) return;

    QRect rect = this->rect().adjusted(5, 5, -5, -5);
    float clickX = event->pos().x() - rect.left();
    float timeRatio = clickX / rect.width();
    float clickTime = timeRatio * m_duration;

    emit timeClicked(clickTime);
}

QSize TimelineWidget::sizeHint() const
{
    return QSize(400, TIMELINE_HEIGHT);
}

// Visualizer Implementation
Visualizer::Visualizer(QWidget *parent)
    : QWidget(parent)
{
    setupUI();
}

void Visualizer::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(10);

    // Score section
    m_scoreWidget = new QWidget;
    m_scoreLayout = new QHBoxLayout(m_scoreWidget);

    m_overallGauge = new ScoreGauge;
    m_overallGauge->setTitle("Overall");

    m_faceGauge = new ScoreGauge;
    m_faceGauge->setTitle("Face");

    m_audioGauge = new ScoreGauge;
    m_audioGauge->setTitle("Audio");

    m_scoreLayout->addWidget(m_overallGauge);
    m_scoreLayout->addWidget(m_faceGauge);
    m_scoreLayout->addWidget(m_audioGauge);
    m_scoreLayout->addStretch();

    // Visualization section
    m_visualWidget = new QWidget;
    m_visualLayout = new QVBoxLayout(m_visualWidget);

    // Face analysis
    m_faceLabel = new QLabel("Face Analysis");
    m_faceLabel->setStyleSheet("QLabel { font-weight: bold; font-size: 14px; }");

    m_thumbnailGrid = new ThumbnailGrid;
    connect(m_thumbnailGrid, &ThumbnailGrid::thumbnailClicked,
            this, &Visualizer::onThumbnailClicked);

    m_thumbnailScrollArea = new QScrollArea;
    m_thumbnailScrollArea->setWidget(m_thumbnailGrid);
    m_thumbnailScrollArea->setWidgetResizable(true);
    m_thumbnailScrollArea->setMaximumHeight(150);
    m_thumbnailScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_thumbnailScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // Audio analysis
    m_audioLabel = new QLabel("Audio Analysis");
    m_audioLabel->setStyleSheet("QLabel { font-weight: bold; font-size: 14px; }");

    m_waveformWidget = new WaveformWidget;
    connect(m_waveformWidget, &WaveformWidget::segmentClicked,
            this, &Visualizer::onSegmentClicked);

    // Timeline
    m_timelineLabel = new QLabel("Timeline");
    m_timelineLabel->setStyleSheet("QLabel { font-weight: bold; font-size: 14px; }");

    m_timelineWidget = new TimelineWidget;
    connect(m_timelineWidget, &TimelineWidget::timeClicked,
            this, &Visualizer::onTimeClicked);

    // Info panel
    m_infoLabel = new QLabel("Select a file to begin analysis");
    m_infoLabel->setStyleSheet("QLabel { color: #B0B0B0; font-style: italic; }");
    m_infoLabel->setWordWrap(true);

    // Add to layout
    m_visualLayout->addWidget(m_faceLabel);
    m_visualLayout->addWidget(m_thumbnailScrollArea);
    m_visualLayout->addWidget(m_audioLabel);
    m_visualLayout->addWidget(m_waveformWidget);
    m_visualLayout->addWidget(m_timelineLabel);
    m_visualLayout->addWidget(m_timelineWidget);
    m_visualLayout->addWidget(m_infoLabel);
    m_visualLayout->addStretch();

    // Main layout
    m_mainLayout->addWidget(m_scoreWidget);
    m_mainLayout->addWidget(m_visualWidget);
}

void Visualizer::setResult(const DetectionResult &result)
{
    m_currentResult = result;
    updateScores(result);
    updateVisualizations(result);
}

void Visualizer::clear()
{
    m_overallGauge->setValue(0);
    m_faceGauge->setValue(0);
    m_audioGauge->setValue(0);

    m_thumbnailGrid->clear();
    m_waveformWidget->clear();
    m_timelineWidget->clear();

    m_infoLabel->setText("Select a file to begin analysis");

    m_currentResult = DetectionResult();
}

void Visualizer::updateScores(const DetectionResult &result)
{
    m_overallGauge->setValue(result.overallScore);
    m_faceGauge->setValue(result.faceScore);
    m_audioGauge->setValue(result.audioScore);
}

void Visualizer::updateVisualizations(const DetectionResult &result)
{
    // Update face detections
    m_thumbnailGrid->setFaceDetections(result.faceDetections);

    // Update audio waveform
    m_waveformWidget->setWaveform(result.audioWaveform);
    m_waveformWidget->setSuspiciousSegments(result.audioSegments);

    // Update timeline
    m_timelineWidget->setSuspiciousFrames(result.suspiciousFrames);
    m_timelineWidget->setDuration(result.audioDuration);

    // Update info
    QString infoText = QString("File: %1\n").arg(QFileInfo(result.filePath).fileName());
    infoText += QString("Type: %1\n").arg(result.fileType.toUpper());
    infoText += QString("Processing time: %1\n").arg(result.processingTime);

    if (result.totalFrames > 0) {
        infoText += QString("Frames: %1 total, %2 processed\n")
                   .arg(result.totalFrames).arg(result.processedFrames);
    }

    if (result.audioDuration > 0) {
        infoText += QString("Duration: %1s\n").arg(result.audioDuration, 0, 'f', 1);
    }

    if (!result.warnings.isEmpty()) {
        infoText += "\nWarnings:\n";
        for (const QString &warning : result.warnings) {
            infoText += "• " + warning + "\n";
        }
    }

    m_infoLabel->setText(infoText);
}

void Visualizer::onThumbnailClicked(int detectionIndex)
{
    if (detectionIndex >= 0 && detectionIndex < m_currentResult.faceDetections.size()) {
        const FaceDetection &detection = m_currentResult.faceDetections[detectionIndex];

        QString tooltip = QString("Frame %1\nScore: %2%\nConfidence: %3%")
                         .arg(detection.frameIndex)
                         .arg(detection.deepfakeScore * 100, 0, 'f', 1)
                         .arg(detection.confidence * 100, 0, 'f', 1);

        QToolTip::showText(QCursor::pos(), tooltip);
    }
}

void Visualizer::onSegmentClicked(int segmentIndex)
{
    if (segmentIndex >= 0 && segmentIndex < m_currentResult.audioSegments.size()) {
        const AudioSegment &segment = m_currentResult.audioSegments[segmentIndex];

        QString tooltip = QString("Time: %1s - %2s\nScore: %3%\nConfidence: %4%")
                         .arg(segment.startTime, 0, 'f', 1)
                         .arg(segment.endTime, 0, 'f', 1)
                         .arg(segment.deepfakeScore * 100, 0, 'f', 1)
                         .arg(segment.confidence * 100, 0, 'f', 1);

        QToolTip::showText(QCursor::pos(), tooltip);
    }
}

void Visualizer::onTimeClicked(float time)
{
    QString tooltip = QString("Time: %1s").arg(time, 0, 'f', 2);
    QToolTip::showText(QCursor::pos(), tooltip);
}


