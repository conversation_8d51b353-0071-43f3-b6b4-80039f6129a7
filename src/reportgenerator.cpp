#include "reportgenerator.h"
#include <QDebug>
#include <QStandardPaths>
#include <QBuffer>
#include <QImageWriter>
#include <QPainterPath>
#include <QLinearGradient>
#include <cmath>

ReportGenerator::ReportGenerator(QObject *parent)
    : QObject(parent)
{
    setupTextFormats();
}

bool ReportGenerator::generateReport(const DetectionResult &result, const QString &outputPath)
{
    QString extension = getFileExtension(outputPath).toLower();
    
    if (extension == "pdf") {
        return generatePdfReport(result, outputPath);
    } else if (extension == "png" || extension == "jpg" || extension == "jpeg") {
        return generateImageReport(result, outputPath);
    } else {
        qWarning() << "Unsupported output format:" << extension;
        return false;
    }
}

bool ReportGenerator::generateScreenshot(QWidget *widget, const QString &outputPath)
{
    if (!widget) {
        return false;
    }
    
    QPixmap screenshot = widget->grab();
    return screenshot.save(outputPath);
}

bool ReportGenerator::generatePdfReport(const DetectionResult &result, const QString &outputPath)
{
    QPdfWriter pdfWriter(outputPath);
    pdfWriter.setPageSize(QPageSize::A4);
    pdfWriter.setPageMargins(QMarginsF(20, 20, 20, 20), QPageLayout::Millimeter);
    
    QTextDocument document;
    QTextCursor cursor(&document);
    
    // Generate report content
    addReportHeader(cursor, result);
    addExecutiveSummary(cursor, result);
    addDetailedAnalysis(cursor, result);
    addFaceAnalysisSection(cursor, result);
    addAudioAnalysisSection(cursor, result);
    addTechnicalDetails(cursor, result);
    addFooter(cursor);
    
    // Print to PDF
    document.print(&pdfWriter);
    
    return true;
}

void ReportGenerator::addReportHeader(QTextCursor &cursor, const DetectionResult &result)
{
    cursor.setCharFormat(m_headerFormat);
    cursor.setBlockFormat(m_centerFormat);
    cursor.insertText("DEEPFAKE ANALYSIS REPORT\n\n");
    
    cursor.setCharFormat(m_subHeaderFormat);
    cursor.insertText("AI-Powered Media Authentication\n\n");
    
    cursor.setBlockFormat(m_leftFormat);
    cursor.setCharFormat(m_normalFormat);
    
    // File information table
    QTextTableFormat tableFormat;
    tableFormat.setBorderStyle(QTextFrameFormat::BorderStyle_Solid);
    tableFormat.setBorder(1);
    tableFormat.setCellPadding(5);
    
    QTextTable *infoTable = cursor.insertTable(4, 2, tableFormat);
    
    infoTable->cellAt(0, 0).firstCursorPosition().insertText("File Name:");
    infoTable->cellAt(0, 1).firstCursorPosition().insertText(QFileInfo(result.filePath).fileName());
    
    infoTable->cellAt(1, 0).firstCursorPosition().insertText("File Type:");
    infoTable->cellAt(1, 1).firstCursorPosition().insertText(result.fileType.toUpper());
    
    infoTable->cellAt(2, 0).firstCursorPosition().insertText("Analysis Date:");
    infoTable->cellAt(2, 1).firstCursorPosition().insertText(QDateTime::currentDateTime().toString());
    
    infoTable->cellAt(3, 0).firstCursorPosition().insertText("Processing Time:");
    infoTable->cellAt(3, 1).firstCursorPosition().insertText(result.processingTime);
    
    cursor.movePosition(QTextCursor::End);
    cursor.insertText("\n\n");
}

void ReportGenerator::addExecutiveSummary(QTextCursor &cursor, const DetectionResult &result)
{
    cursor.setCharFormat(m_subHeaderFormat);
    cursor.insertText("EXECUTIVE SUMMARY\n\n");
    
    cursor.setCharFormat(m_normalFormat);
    
    QString riskLevel = getRiskLevel(result.overallScore);
    QString confidenceLevel = getConfidenceLevel(result.confidence);
    
    cursor.insertText(QString("Overall Deepfake Score: %1% (%2 Risk)\n")
                     .arg(result.overallScore, 0, 'f', 1)
                     .arg(riskLevel));
    
    cursor.insertText(QString("Analysis Confidence: %1% (%2)\n\n")
                     .arg(result.confidence, 0, 'f', 1)
                     .arg(confidenceLevel));
    
    // Risk assessment
    cursor.setCharFormat(m_boldFormat);
    cursor.insertText("Risk Assessment:\n");
    cursor.setCharFormat(m_normalFormat);
    
    if (result.overallScore < 30) {
        cursor.insertText("• The analyzed media shows LOW likelihood of being synthetic/deepfake content.\n");
        cursor.insertText("• Most indicators suggest authentic media with natural characteristics.\n");
    } else if (result.overallScore < 70) {
        cursor.insertText("• The analyzed media shows MODERATE likelihood of being synthetic/deepfake content.\n");
        cursor.insertText("• Some suspicious indicators detected - further investigation recommended.\n");
    } else {
        cursor.insertText("• The analyzed media shows HIGH likelihood of being synthetic/deepfake content.\n");
        cursor.insertText("• Multiple suspicious indicators detected - likely artificial generation.\n");
    }
    
    cursor.insertText("\n");
}

void ReportGenerator::addDetailedAnalysis(QTextCursor &cursor, const DetectionResult &result)
{
    cursor.setCharFormat(m_subHeaderFormat);
    cursor.insertText("DETAILED ANALYSIS\n\n");
    
    cursor.setCharFormat(m_normalFormat);
    
    // Scores breakdown
    QTextTableFormat tableFormat;
    tableFormat.setBorderStyle(QTextFrameFormat::BorderStyle_Solid);
    tableFormat.setBorder(1);
    tableFormat.setCellPadding(5);
    
    QTextTable *scoresTable = cursor.insertTable(4, 3, tableFormat);
    
    // Headers
    scoresTable->cellAt(0, 0).firstCursorPosition().insertText("Analysis Type");
    scoresTable->cellAt(0, 1).firstCursorPosition().insertText("Score");
    scoresTable->cellAt(0, 2).firstCursorPosition().insertText("Confidence");
    
    // Overall
    scoresTable->cellAt(1, 0).firstCursorPosition().insertText("Overall");
    scoresTable->cellAt(1, 1).firstCursorPosition().insertText(QString("%1%").arg(result.overallScore, 0, 'f', 1));
    scoresTable->cellAt(1, 2).firstCursorPosition().insertText(QString("%1%").arg(result.confidence, 0, 'f', 1));
    
    // Face
    scoresTable->cellAt(2, 0).firstCursorPosition().insertText("Face Analysis");
    scoresTable->cellAt(2, 1).firstCursorPosition().insertText(QString("%1%").arg(result.faceScore, 0, 'f', 1));
    scoresTable->cellAt(2, 2).firstCursorPosition().insertText(QString("%1%").arg(result.faceConfidence, 0, 'f', 1));
    
    // Audio
    scoresTable->cellAt(3, 0).firstCursorPosition().insertText("Audio Analysis");
    scoresTable->cellAt(3, 1).firstCursorPosition().insertText(QString("%1%").arg(result.audioScore, 0, 'f', 1));
    scoresTable->cellAt(3, 2).firstCursorPosition().insertText(QString("%1%").arg(result.audioConfidence, 0, 'f', 1));
    
    cursor.movePosition(QTextCursor::End);
    cursor.insertText("\n\n");
}

void ReportGenerator::addFaceAnalysisSection(QTextCursor &cursor, const DetectionResult &result)
{
    cursor.setCharFormat(m_subHeaderFormat);
    cursor.insertText("FACE ANALYSIS\n\n");
    
    cursor.setCharFormat(m_normalFormat);
    
    if (result.faceDetections.isEmpty()) {
        cursor.insertText("No faces detected in the analyzed media.\n\n");
        return;
    }
    
    cursor.insertText(QString("Total faces detected: %1\n").arg(result.faceDetections.size()));
    cursor.insertText(QString("Frames analyzed: %1 of %2\n\n").arg(result.processedFrames).arg(result.totalFrames));
    
    // Face detection summary
    int suspiciousFaces = 0;
    for (const auto& detection : result.faceDetections) {
        if (detection.deepfakeScore > 0.5f) {
            suspiciousFaces++;
        }
    }
    
    cursor.insertText(QString("Suspicious faces: %1 (%2%)\n")
                     .arg(suspiciousFaces)
                     .arg(suspiciousFaces * 100.0 / result.faceDetections.size(), 0, 'f', 1));
    
    cursor.insertText("\n");
}

void ReportGenerator::addAudioAnalysisSection(QTextCursor &cursor, const DetectionResult &result)
{
    cursor.setCharFormat(m_subHeaderFormat);
    cursor.insertText("AUDIO ANALYSIS\n\n");

    cursor.setCharFormat(m_normalFormat);

    if (result.audioSegments.isEmpty()) {
        cursor.insertText("No audio data analyzed.\n\n");
        return;
    }

    cursor.insertText(QString("Audio duration: %1\n").arg(formatDuration(result.audioDuration)));
    cursor.insertText(QString("Segments analyzed: %1\n\n").arg(result.audioSegments.size()));

    // Audio analysis summary
    int suspiciousSegments = 0;
    for (const auto& segment : result.audioSegments) {
        if (segment.deepfakeScore > 0.5f) {
            suspiciousSegments++;
        }
    }

    cursor.insertText(QString("Suspicious segments: %1 (%2%)\n")
                     .arg(suspiciousSegments)
                     .arg(suspiciousSegments * 100.0 / result.audioSegments.size(), 0, 'f', 1));

    cursor.insertText("\n");
}

void ReportGenerator::addTechnicalDetails(QTextCursor &cursor, const DetectionResult &result)
{
    cursor.setCharFormat(m_subHeaderFormat);
    cursor.insertText("TECHNICAL DETAILS\n\n");

    cursor.setCharFormat(m_normalFormat);

    cursor.setCharFormat(m_boldFormat);
    cursor.insertText("Analysis Methods:\n");
    cursor.setCharFormat(m_normalFormat);
    cursor.insertText("• Computer vision-based face detection and analysis\n");
    cursor.insertText("• Audio feature extraction and machine learning classification\n");
    cursor.insertText("• Multi-modal fusion for comprehensive scoring\n\n");

    if (!result.warnings.isEmpty()) {
        cursor.setCharFormat(m_boldFormat);
        cursor.insertText("Warnings:\n");
        cursor.setCharFormat(m_normalFormat);
        for (const QString& warning : result.warnings) {
            cursor.insertText("• " + warning + "\n");
        }
        cursor.insertText("\n");
    }

    cursor.setCharFormat(m_boldFormat);
    cursor.insertText("Model Information:\n");
    cursor.setCharFormat(m_normalFormat);
    cursor.insertText("• Face Detection: OpenCV Haar Cascades + ONNX Neural Network\n");
    cursor.insertText("• Voice Analysis: Audio Feature Extraction + ONNX Classification\n");
    cursor.insertText("• Framework: Qt6 + OpenCV + ONNX Runtime\n\n");
}

void ReportGenerator::addFooter(QTextCursor &cursor)
{
    cursor.insertText("\n");
    cursor.setBlockFormat(m_centerFormat);
    cursor.setCharFormat(m_italicFormat);
    cursor.insertText("Generated by Deepfake Predictor v1.0\n");
    cursor.insertText("This report is for informational purposes only.\n");
    cursor.insertText("Results should be verified by human experts for critical applications.");
}

bool ReportGenerator::generateImageReport(const DetectionResult &result, const QString &outputPath)
{
    QPixmap reportImage = createReportImage(result);
    return reportImage.save(outputPath);
}

QPixmap ReportGenerator::createReportImage(const DetectionResult &result)
{
    int imageHeight = 800;
    QPixmap image(REPORT_WIDTH, imageHeight);
    image.fill(QColor(45, 45, 45));

    QPainter painter(&image);
    painter.setRenderHint(QPainter::Antialiasing);

    int y = REPORT_MARGIN;

    // Header
    painter.setPen(Qt::white);
    QFont headerFont = painter.font();
    headerFont.setPointSize(18);
    headerFont.setBold(true);
    painter.setFont(headerFont);

    painter.drawText(REPORT_MARGIN, y, "Deepfake Analysis Report");
    y += 40;

    // File info
    QFont normalFont = painter.font();
    normalFont.setPointSize(12);
    normalFont.setBold(false);
    painter.setFont(normalFont);

    painter.drawText(REPORT_MARGIN, y, QString("File: %1").arg(QFileInfo(result.filePath).fileName()));
    y += 25;
    painter.drawText(REPORT_MARGIN, y, QString("Type: %1").arg(result.fileType.toUpper()));
    y += 25;
    painter.drawText(REPORT_MARGIN, y, QString("Analysis Date: %1").arg(QDateTime::currentDateTime().toString()));
    y += 40;

    // Score visualization
    QPixmap scoreViz = createScoreVisualization(result);
    painter.drawPixmap(REPORT_MARGIN, y, scoreViz);
    y += scoreViz.height() + 30;

    // Face thumbnails
    if (!result.faceDetections.isEmpty()) {
        painter.drawText(REPORT_MARGIN, y, "Face Analysis:");
        y += 25;

        QPixmap faceGrid = createFaceThumbnailGrid(result.faceDetections);
        painter.drawPixmap(REPORT_MARGIN, y, faceGrid);
        y += faceGrid.height() + 20;
    }

    // Audio waveform
    if (!result.audioWaveform.isEmpty()) {
        painter.drawText(REPORT_MARGIN, y, "Audio Analysis:");
        y += 25;

        QPixmap waveform = createAudioWaveform(result.audioWaveform, result.audioSegments);
        painter.drawPixmap(REPORT_MARGIN, y, waveform);
        y += waveform.height() + 20;
    }

    // Timeline
    if (!result.suspiciousFrames.isEmpty()) {
        painter.drawText(REPORT_MARGIN, y, "Timeline:");
        y += 25;

        QPixmap timeline = createTimeline(result.suspiciousFrames);
        painter.drawPixmap(REPORT_MARGIN, y, timeline);
    }

    return image;
}

QPixmap ReportGenerator::createScoreVisualization(const DetectionResult &result)
{
    QPixmap scoreImage(300, 100);
    scoreImage.fill(Qt::transparent);

    QPainter painter(&scoreImage);
    painter.setRenderHint(QPainter::Antialiasing);

    // Draw score bars
    QStringList labels = {"Overall", "Face", "Audio"};
    QVector<float> scores = {result.overallScore, result.faceScore, result.audioScore};

    int barWidth = 80;
    int barHeight = 20;
    int spacing = 10;

    for (int i = 0; i < 3; i++) {
        int x = i * (barWidth + spacing);
        int y = 30;

        // Background
        painter.fillRect(x, y, barWidth, barHeight, QColor(60, 60, 60));

        // Score bar
        int scoreWidth = static_cast<int>((scores[i] / 100.0f) * barWidth);
        QColor scoreColor = getScoreColor(scores[i]);
        painter.fillRect(x, y, scoreWidth, barHeight, scoreColor);

        // Label
        painter.setPen(Qt::white);
        painter.drawText(x, y - 5, labels[i]);

        // Score text
        painter.drawText(x, y + barHeight + 15, QString("%1%").arg(scores[i], 0, 'f', 1));
    }

    return scoreImage;
}

QPixmap ReportGenerator::createFaceThumbnailGrid(const QVector<FaceDetection> &detections)
{
    if (detections.isEmpty()) {
        return QPixmap();
    }

    int cols = 6;
    int rows = (detections.size() + cols - 1) / cols;
    int gridWidth = cols * (THUMBNAIL_SIZE + 5) - 5;
    int gridHeight = rows * (THUMBNAIL_SIZE + 5) - 5;

    QPixmap grid(gridWidth, gridHeight);
    grid.fill(Qt::transparent);

    QPainter painter(&grid);

    for (int i = 0; i < detections.size(); i++) {
        int row = i / cols;
        int col = i % cols;
        int x = col * (THUMBNAIL_SIZE + 5);
        int y = row * (THUMBNAIL_SIZE + 5);

        const FaceDetection &detection = detections[i];

        if (!detection.thumbnail.isNull()) {
            QPixmap thumb = detection.thumbnail.scaled(THUMBNAIL_SIZE, THUMBNAIL_SIZE,
                                                      Qt::KeepAspectRatio, Qt::SmoothTransformation);
            painter.drawPixmap(x, y, thumb);

            // Score overlay
            QColor scoreColor = getScoreColor(detection.deepfakeScore * 100);
            painter.fillRect(x, y + THUMBNAIL_SIZE - 12, THUMBNAIL_SIZE, 12, QColor(0, 0, 0, 150));
            painter.setPen(Qt::white);
            painter.drawText(x + 2, y + THUMBNAIL_SIZE - 2,
                           QString("%1%").arg(detection.deepfakeScore * 100, 0, 'f', 0));
        }
    }

    return grid;
}

QPixmap ReportGenerator::createAudioWaveform(const QVector<float> &waveform,
                                           const QVector<AudioSegment> &segments)
{
    if (waveform.isEmpty()) {
        return QPixmap();
    }

    QPixmap waveImage(REPORT_WIDTH - 2 * REPORT_MARGIN, WAVEFORM_HEIGHT);
    waveImage.fill(QColor(30, 30, 30));

    QPainter painter(&waveImage);
    painter.setRenderHint(QPainter::Antialiasing);

    // Draw waveform
    painter.setPen(QColor(100, 200, 255));
    int centerY = WAVEFORM_HEIGHT / 2;
    float xStep = static_cast<float>(waveImage.width()) / waveform.size();

    for (int i = 1; i < waveform.size(); i++) {
        float x1 = (i - 1) * xStep;
        float x2 = i * xStep;
        float y1 = centerY - waveform[i - 1] * WAVEFORM_HEIGHT / 4;
        float y2 = centerY - waveform[i] * WAVEFORM_HEIGHT / 4;

        painter.drawLine(QPointF(x1, y1), QPointF(x2, y2));
    }

    // Highlight suspicious segments
    for (const AudioSegment &segment : segments) {
        if (segment.deepfakeScore > 0.5f) {
            float startX = (segment.startTime / 10.0f) * waveImage.width();
            float endX = (segment.endTime / 10.0f) * waveImage.width();

            painter.fillRect(QRectF(startX, 0, endX - startX, WAVEFORM_HEIGHT),
                           QColor(255, 0, 0, 100));
        }
    }

    return waveImage;
}

QPixmap ReportGenerator::createTimeline(const QVector<bool> &suspiciousFrames)
{
    if (suspiciousFrames.isEmpty()) {
        return QPixmap();
    }

    QPixmap timeline(REPORT_WIDTH - 2 * REPORT_MARGIN, TIMELINE_HEIGHT);
    timeline.fill(QColor(40, 40, 40));

    QPainter painter(&timeline);

    float segmentWidth = static_cast<float>(timeline.width()) / suspiciousFrames.size();

    for (int i = 0; i < suspiciousFrames.size(); i++) {
        float x = i * segmentWidth;
        QRectF segmentRect(x, 0, segmentWidth, TIMELINE_HEIGHT);

        QColor segmentColor = suspiciousFrames[i] ?
                             QColor(244, 67, 54, 180) :  // Red for suspicious
                             QColor(76, 175, 80, 100);   // Green for normal

        painter.fillRect(segmentRect, segmentColor);
    }

    return timeline;
}

QString ReportGenerator::getFileExtension(const QString &filePath)
{
    return QFileInfo(filePath).suffix();
}

QString ReportGenerator::formatDuration(float seconds)
{
    int mins = static_cast<int>(seconds) / 60;
    int secs = static_cast<int>(seconds) % 60;
    return QString("%1:%2").arg(mins).arg(secs, 2, 10, QChar('0'));
}

QString ReportGenerator::getConfidenceLevel(float confidence)
{
    if (confidence >= 80) return "High";
    if (confidence >= 60) return "Medium";
    if (confidence >= 40) return "Low";
    return "Very Low";
}

QString ReportGenerator::getRiskLevel(float score)
{
    if (score < 30) return "Low";
    if (score < 70) return "Medium";
    return "High";
}

QColor ReportGenerator::getScoreColor(float score)
{
    if (score < 30) return QColor(76, 175, 80);   // Green
    if (score < 70) return QColor(255, 152, 0);   // Orange
    return QColor(244, 67, 54);                   // Red
}

void ReportGenerator::setupTextFormats()
{
    // Header format
    m_headerFormat.setFontPointSize(18);
    m_headerFormat.setFontWeight(QFont::Bold);
    m_headerFormat.setForeground(QBrush(Qt::black));

    // Sub-header format
    m_subHeaderFormat.setFontPointSize(14);
    m_subHeaderFormat.setFontWeight(QFont::Bold);
    m_subHeaderFormat.setForeground(QBrush(Qt::black));

    // Normal format
    m_normalFormat.setFontPointSize(11);
    m_normalFormat.setFontWeight(QFont::Normal);
    m_normalFormat.setForeground(QBrush(Qt::black));

    // Bold format
    m_boldFormat.setFontPointSize(11);
    m_boldFormat.setFontWeight(QFont::Bold);
    m_boldFormat.setForeground(QBrush(Qt::black));

    // Italic format
    m_italicFormat.setFontPointSize(10);
    m_italicFormat.setFontItalic(true);
    m_italicFormat.setForeground(QBrush(Qt::gray));

    // Code format
    m_codeFormat.setFontFamily("Courier New");
    m_codeFormat.setFontPointSize(10);
    m_codeFormat.setBackground(QBrush(QColor(240, 240, 240)));

    // Block formats
    m_centerFormat.setAlignment(Qt::AlignCenter);
    m_leftFormat.setAlignment(Qt::AlignLeft);
}
