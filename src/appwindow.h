#ifndef APPWINDOW_H
#define APPWINDOW_H

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QProgressBar>
#include <QTextEdit>
#include <QScrollArea>
#include <QFrame>
#include <QGroupBox>
#include <QSplitter>
#include <QFileDialog>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QMimeData>
#include <QUrl>
#include <QTimer>
#include <QThread>
#include <QMutex>
#include <QStatusBar>
#include <QMenuBar>
#include <QAction>

#include "deepfakedetector.h"
#include "visualizer.h"
#include "reportgenerator.h"

class DropArea : public QFrame
{
    Q_OBJECT

public:
    explicit DropArea(QWidget *parent = nullptr);

signals:
    void fileDropped(const QString &filePath);

protected:
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dragMoveEvent(QDragMoveEvent *event) override;
    void dropEvent(QDropEvent *event) override;
    void paintEvent(QPaintEvent *event) override;

private:
    bool m_dragActive;
    QString m_supportedFormats;
};

class AppWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit AppWindow(QWidget *parent = nullptr);
    ~AppWindow();

    void loadFile(const QString &filePath);

private slots:
    void onFileDropped(const QString &filePath);
    void onOpenFile();
    void onAnalyzeFile();
    void onSaveReport();
    void onAbout();
    void onDetectionProgress(int percentage);
    void onDetectionComplete(const DetectionResult &result);
    void onDetectionError(const QString &error);

private:
    void setupUI();
    void setupMenuBar();
    void setupStatusBar();
    void resetUI();
    void updateUI(const DetectionResult &result);
    void setAnalyzing(bool analyzing);

    // UI Components
    QWidget *m_centralWidget;
    QSplitter *m_mainSplitter;
    QSplitter *m_rightSplitter;
    
    // Left panel - Input and controls
    QGroupBox *m_inputGroup;
    DropArea *m_dropArea;
    QPushButton *m_openButton;
    QPushButton *m_analyzeButton;
    QPushButton *m_saveReportButton;
    QLabel *m_fileInfoLabel;
    
    // Right panel - Results
    QGroupBox *m_resultsGroup;
    QLabel *m_scoreLabel;
    QProgressBar *m_scoreBar;
    QLabel *m_confidenceLabel;
    
    // Analysis progress
    QGroupBox *m_progressGroup;
    QProgressBar *m_analysisProgress;
    QLabel *m_progressLabel;
    QTextEdit *m_logOutput;
    
    // Visualization area
    QGroupBox *m_visualGroup;
    Visualizer *m_visualizer;
    
    // Core components
    DeepfakeDetector *m_detector;
    ReportGenerator *m_reportGenerator;
    QThread *m_detectorThread;
    
    // State
    QString m_currentFile;
    DetectionResult m_lastResult;
    bool m_isAnalyzing;
    
    // UI Constants
    static const int WINDOW_MIN_WIDTH = 1200;
    static const int WINDOW_MIN_HEIGHT = 800;
    static const int LEFT_PANEL_WIDTH = 350;
};

#endif // APPWINDOW_H
