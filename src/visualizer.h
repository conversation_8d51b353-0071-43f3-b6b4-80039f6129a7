#ifndef VISUALIZER_H
#define VISUALIZER_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QScrollArea>
#include <QFrame>
#include <QPainter>
#include <QPixmap>
#include <QTimer>
#include <QPropertyAnimation>
#include <QGraphicsEffect>
#include <QMouseEvent>

#include "deepfakedetector.h"

class ScoreGauge : public QWidget
{
    Q_OBJECT
    Q_PROPERTY(float value READ value WRITE setValue)

public:
    explicit ScoreGauge(QWidget *parent = nullptr);
    
    float value() const { return m_value; }
    void setValue(float value);
    
    void setAnimated(bool animated) { m_animated = animated; }
    void setTitle(const QString &title) { m_title = title; }

protected:
    void paintEvent(QPaintEvent *event) override;
    QSize sizeHint() const override;

private:
    float m_value;
    float m_targetValue;
    QString m_title;
    bool m_animated;
    QPropertyAnimation *m_animation;
    
    static const int GAUGE_SIZE = 120;
};

class WaveformWidget : public QWidget
{
    Q_OBJECT

public:
    explicit WaveformWidget(QWidget *parent = nullptr);
    
    void setWaveform(const QVector<float> &waveform);
    void setSuspiciousSegments(const QVector<AudioSegment> &segments);
    void clear();

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    QSize sizeHint() const override;

signals:
    void segmentClicked(int segmentIndex);

private:
    QVector<float> m_waveform;
    QVector<AudioSegment> m_suspiciousSegments;
    int m_hoveredSegment;
    
    static const int WAVEFORM_HEIGHT = 80;
};

class ThumbnailGrid : public QWidget
{
    Q_OBJECT

public:
    explicit ThumbnailGrid(QWidget *parent = nullptr);
    
    void setFaceDetections(const QVector<FaceDetection> &detections);
    void clear();

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    QSize sizeHint() const override;

signals:
    void thumbnailClicked(int detectionIndex);

private:
    void updateLayout();
    
    QVector<FaceDetection> m_detections;
    QVector<QRect> m_thumbnailRects;
    int m_hoveredThumbnail;
    int m_columns;
    
    static const int THUMBNAIL_SIZE = 64;
    static const int THUMBNAIL_SPACING = 8;
};

class TimelineWidget : public QWidget
{
    Q_OBJECT

public:
    explicit TimelineWidget(QWidget *parent = nullptr);
    
    void setSuspiciousFrames(const QVector<bool> &suspiciousFrames);
    void setDuration(float duration);
    void clear();

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    QSize sizeHint() const override;

signals:
    void timeClicked(float time);

private:
    QVector<bool> m_suspiciousFrames;
    float m_duration;
    float m_hoveredTime;
    
    static const int TIMELINE_HEIGHT = 30;
};

class Visualizer : public QWidget
{
    Q_OBJECT

public:
    explicit Visualizer(QWidget *parent = nullptr);
    
    void setResult(const DetectionResult &result);
    void clear();

private slots:
    void onThumbnailClicked(int detectionIndex);
    void onSegmentClicked(int segmentIndex);
    void onTimeClicked(float time);

private:
    void setupUI();
    void updateScores(const DetectionResult &result);
    void updateVisualizations(const DetectionResult &result);
    
    // UI Components
    QVBoxLayout *m_mainLayout;
    
    // Score section
    QWidget *m_scoreWidget;
    QHBoxLayout *m_scoreLayout;
    ScoreGauge *m_overallGauge;
    ScoreGauge *m_faceGauge;
    ScoreGauge *m_audioGauge;
    
    // Visualization section
    QWidget *m_visualWidget;
    QVBoxLayout *m_visualLayout;
    
    // Face analysis
    QLabel *m_faceLabel;
    ThumbnailGrid *m_thumbnailGrid;
    QScrollArea *m_thumbnailScrollArea;
    
    // Audio analysis
    QLabel *m_audioLabel;
    WaveformWidget *m_waveformWidget;
    
    // Timeline
    QLabel *m_timelineLabel;
    TimelineWidget *m_timelineWidget;
    
    // Info panel
    QLabel *m_infoLabel;
    
    // Current result
    DetectionResult m_currentResult;
};

#endif // VISUALIZER_H
