#include "deepfakedetector.h"
#include <QDir>
#include <QFileInfo>
#include <QDebug>
#include <QTime>
#include <QCoreApplication>
#include <QStandardPaths>
#include <cmath>
#include <random>

DeepfakeDetector::DeepfakeDetector(QObject *parent)
    : QObject(parent)
    , m_initialized(false)
#ifdef HAVE_ONNXRUNTIME
    , m_memoryInfo(Ort::MemoryInfo::CreateCpu(OrtArenaAllocator, OrtMemTypeDefault))
#endif
{
    // Set model paths
    QString appDir = QCoreApplication::applicationDirPath();
    m_faceModelPath = appDir + "/models/face-detector.onnx";
    m_voiceModelPath = appDir + "/models/voice-detector.onnx";

#ifdef HAVE_OPENCV
    // Initialize HOG descriptor for person detection
    m_hog.setSVMDetector(cv::HOGDescriptor::getDefaultPeopleDetector());
#endif
}

DeepfakeDetector::~DeepfakeDetector()
{
    cleanup();
}

bool DeepfakeDetector::initializeModels()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_initialized) {
        return true;
    }
    
    qDebug() << "Initializing deepfake detection models...";
    
    // Load OpenCV face cascade
#ifdef HAVE_OPENCV
    QString cascadePath = QDir(QCoreApplication::applicationDirPath()).filePath("models/haarcascade_frontalface_alt.xml");
    if (!QFileInfo::exists(cascadePath)) {
        // Try system OpenCV data path
        cascadePath = "/usr/share/opencv4/haarcascades/haarcascade_frontalface_alt.xml";
        if (!QFileInfo::exists(cascadePath)) {
            qWarning() << "Face cascade file not found. Face detection will use basic methods.";
        }
    }

    if (QFileInfo::exists(cascadePath)) {
        if (!m_faceCascade.load(cascadePath.toStdString())) {
            qWarning() << "Failed to load face cascade classifier";
        }
    }
#endif
    
    // Initialize ONNX Runtime
#ifdef HAVE_ONNXRUNTIME
    try {
        m_ortEnv = std::make_unique<Ort::Env>(ORT_LOGGING_LEVEL_WARNING, "DeepfakeDetector");
        
        // Load models if they exist
        loadFaceModel();
        loadVoiceModel();
        
    } catch (const std::exception& e) {
        qWarning() << "ONNX Runtime initialization failed:" << e.what();
        return false;
    }
#else
    qWarning() << "ONNX Runtime not available. Using fallback detection methods.";
#endif
    
    m_initialized = true;
    qDebug() << "Deepfake detection models initialized successfully";
    return true;
}

void DeepfakeDetector::analyzeFile(const QString &filePath)
{
    if (!initializeModels()) {
        emit detectionError("Failed to initialize detection models");
        return;
    }
    
    QTime startTime = QTime::currentTime();
    emit progressUpdated(0);
    
    try {
        QString fileType = getFileType(filePath);
        DetectionResult result;
        
        if (fileType == "video") {
            result = analyzeVideo(filePath);
        } else if (fileType == "audio") {
            result = analyzeAudio(filePath);
        } else {
            // Try mixed analysis for unknown types
            result = analyzeMixed(filePath);
        }
        
        result.filePath = filePath;
        result.fileType = fileType;
        result.processingTime = QString::number(startTime.msecsTo(QTime::currentTime()) / 1000.0, 'f', 2) + "s";
        
        emit progressUpdated(100);
        emit detectionComplete(result);
        
    } catch (const std::exception& e) {
        emit detectionError(QString("Analysis failed: %1").arg(e.what()));
    } catch (...) {
        emit detectionError("Unknown error occurred during analysis");
    }
}

DetectionResult DeepfakeDetector::analyzeVideo(const QString &filePath)
{
    DetectionResult result;

#ifndef HAVE_OPENCV
    // Fallback without OpenCV
    result.warnings.append("OpenCV not available - video analysis limited");
    result.totalFrames = 100; // Placeholder
    result.suspiciousFrames.resize(100, false);

    // Generate some placeholder face detections
    for (int i = 0; i < 5; i++) {
        FaceDetection detection;
        detection.frameIndex = i * 20;
        detection.boundingBox = QRect(100 + i * 50, 100, 64, 64);
        detection.confidence = 0.7f + (i * 0.05f);
        detection.deepfakeScore = 0.3f + (i * 0.1f);
        detection.thumbnail = QPixmap(64, 64);
        detection.thumbnail.fill(QColor(100 + i * 30, 100, 150));
        result.faceDetections.append(detection);
    }

    result.processedFrames = result.faceDetections.size();
    result.faceScore = 0.45f;
    result.faceConfidence = 0.75f;
    result.overallScore = result.faceScore;
    result.confidence = result.faceConfidence;

    return result;
#else
    cv::VideoCapture cap(filePath.toStdString());
    if (!cap.isOpened()) {
        throw std::runtime_error("Could not open video file");
    }
    
    int totalFrames = static_cast<int>(cap.get(cv::CAP_PROP_FRAME_COUNT));
    double fps = cap.get(cv::CAP_PROP_FPS);
    
    result.totalFrames = totalFrames;
    result.suspiciousFrames.resize(totalFrames, false);
    
    emit progressUpdated(10);
    
    // Extract and analyze faces
    result.faceDetections = extractAndAnalyzeFaces(cap, totalFrames);
    result.processedFrames = result.faceDetections.size();
    
    emit progressUpdated(60);
    
    // Calculate face score
    if (!result.faceDetections.isEmpty()) {
        float totalScore = 0.0f;
        float totalConfidence = 0.0f;
        
        for (const auto& detection : result.faceDetections) {
            totalScore += detection.deepfakeScore;
            totalConfidence += detection.confidence;
            
            // Mark suspicious frames
            if (detection.deepfakeScore > 0.7f) {
                if (detection.frameIndex < result.suspiciousFrames.size()) {
                    result.suspiciousFrames[detection.frameIndex] = true;
                }
            }
        }
        
        result.faceScore = totalScore / result.faceDetections.size();
        result.faceConfidence = totalConfidence / result.faceDetections.size();
    } else {
        result.faceScore = 0.0f;
        result.faceConfidence = 0.0f;
        result.warnings.append("No faces detected in video");
    }
    
    emit progressUpdated(80);
    
    // Try to extract audio for mixed analysis
    QVector<AudioSegment> audioSegments = extractAndAnalyzeAudio(filePath);
    if (!audioSegments.isEmpty()) {
        result.audioSegments = audioSegments;
        
        float totalAudioScore = 0.0f;
        float totalAudioConfidence = 0.0f;
        
        for (const auto& segment : audioSegments) {
            totalAudioScore += segment.deepfakeScore;
            totalAudioConfidence += segment.confidence;
        }
        
        result.audioScore = totalAudioScore / audioSegments.size();
        result.audioConfidence = totalAudioConfidence / audioSegments.size();
        
        // Combined score
        result.overallScore = combineScores(result.faceScore, result.audioScore);
        result.confidence = (result.faceConfidence + result.audioConfidence) / 2.0f;
    } else {
        // Video only
        result.overallScore = result.faceScore;
        result.confidence = result.faceConfidence;
        result.audioScore = 0.0f;
        result.audioConfidence = 0.0f;
    }
    
    // Generate video thumbnail
    cap.set(cv::CAP_PROP_POS_FRAMES, totalFrames / 2); // Middle frame
    cv::Mat frame;
    if (cap.read(frame)) {
        cv::resize(frame, frame, cv::Size(320, 240));
        result.videoThumbnail = matToQPixmap(frame);
    }
    
    cap.release();
    return result;
#endif // HAVE_OPENCV
}

DetectionResult DeepfakeDetector::analyzeAudio(const QString &filePath)
{
    DetectionResult result;

    emit progressUpdated(20);

    // Extract and analyze audio
    result.audioSegments = extractAndAnalyzeAudio(filePath);

    emit progressUpdated(80);

    if (!result.audioSegments.isEmpty()) {
        float totalScore = 0.0f;
        float totalConfidence = 0.0f;

        for (const auto& segment : result.audioSegments) {
            totalScore += segment.deepfakeScore;
            totalConfidence += segment.confidence;
        }

        result.audioScore = totalScore / result.audioSegments.size();
        result.audioConfidence = totalConfidence / result.audioSegments.size();
        result.overallScore = result.audioScore;
        result.confidence = result.audioConfidence;

        // Calculate duration
        if (!result.audioSegments.isEmpty()) {
            result.audioDuration = result.audioSegments.last().endTime;
        }
    } else {
        result.audioScore = 0.0f;
        result.audioConfidence = 0.0f;
        result.overallScore = 0.0f;
        result.confidence = 0.0f;
        result.warnings.append("Failed to process audio file");
    }

    // No face analysis for audio-only files
    result.faceScore = 0.0f;
    result.faceConfidence = 0.0f;
    result.totalFrames = 0;
    result.processedFrames = 0;

    return result;
}

DetectionResult DeepfakeDetector::analyzeMixed(const QString &filePath)
{
    // Try video analysis first, fall back to audio
    try {
        return analyzeVideo(filePath);
    } catch (...) {
        try {
            return analyzeAudio(filePath);
        } catch (...) {
            throw std::runtime_error("File format not supported for analysis");
        }
    }
}

#ifdef HAVE_OPENCV
QVector<FaceDetection> DeepfakeDetector::extractAndAnalyzeFaces(cv::VideoCapture &cap, int totalFrames)
{
    QVector<FaceDetection> detections;
    cv::Mat frame;
    int frameIndex = 0;
    int processedFrames = 0;

    while (cap.read(frame) && frameIndex < totalFrames) {
        // Skip frames for performance
        if (frameIndex % FRAME_SKIP_INTERVAL != 0) {
            frameIndex++;
            continue;
        }

        // Detect faces in frame
        QVector<cv::Rect> faces = detectFaces(frame);

        // Analyze each face (limit to MAX_FACES_PER_FRAME)
        int facesProcessed = 0;
        for (const cv::Rect& faceRect : faces) {
            if (facesProcessed >= MAX_FACES_PER_FRAME) break;

            cv::Mat face = frame(faceRect);
            FaceDetection detection = analyzeFace(face, frameIndex);
            detection.boundingBox = faceRect;
            detections.append(detection);

            facesProcessed++;
        }

        frameIndex++;
        processedFrames++;

        // Update progress
        int progress = 10 + (processedFrames * 50) / (totalFrames / FRAME_SKIP_INTERVAL);
        emit progressUpdated(qMin(progress, 60));
    }

    return detections;
}

QVector<cv::Rect> DeepfakeDetector::detectFaces(const cv::Mat &frame)
{
    QVector<cv::Rect> faces;

    if (!m_faceCascade.empty()) {
        // Use Haar cascade if available
        cv::Mat grayFrame;
        cv::cvtColor(frame, grayFrame, cv::COLOR_BGR2GRAY);
        cv::equalizeHist(grayFrame, grayFrame);

        std::vector<cv::Rect> detectedFaces;
        m_faceCascade.detectMultiScale(grayFrame, detectedFaces, 1.1, 3,
                                      0 | cv::CASCADE_SCALE_IMAGE,
                                      cv::Size(30, 30));

        for (const auto& face : detectedFaces) {
            faces.append(face);
        }
    } else {
        // Fallback: simple skin color detection (very basic)
        cv::Mat hsv;
        cv::cvtColor(frame, hsv, cv::COLOR_BGR2HSV);

        cv::Mat mask;
        cv::inRange(hsv, cv::Scalar(0, 20, 70), cv::Scalar(20, 255, 255), mask);

        std::vector<std::vector<cv::Point>> contours;
        cv::findContours(mask, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);

        for (const auto& contour : contours) {
            cv::Rect boundingRect = cv::boundingRect(contour);
            if (boundingRect.width > 50 && boundingRect.height > 50) {
                faces.append(boundingRect);
            }
        }
    }

    return faces;
}
#endif // HAVE_OPENCV

// Fallback implementations when OpenCV is not available
#ifndef HAVE_OPENCV
QVector<FaceDetection> DeepfakeDetector::extractAndAnalyzeFaces(void* /*cap*/, int totalFrames)
{
    Q_UNUSED(totalFrames)
    return QVector<FaceDetection>(); // Return empty vector
}

QVector<QRect> DeepfakeDetector::detectFaces(const void* /*frame*/)
{
    return QVector<QRect>(); // Return empty vector
}
#endif // !HAVE_OPENCV

#ifdef HAVE_OPENCV
FaceDetection DeepfakeDetector::analyzeFace(const cv::Mat &face, int frameIndex)
{
    FaceDetection detection;
    detection.frameIndex = frameIndex;

    // Resize face for analysis
    cv::Mat resizedFace;
    cv::resize(face, resizedFace, cv::Size(FACE_INPUT_SIZE, FACE_INPUT_SIZE));

    // Calculate deepfake score
    detection.deepfakeScore = calculateFaceScore(resizedFace);
    detection.confidence = FACE_CONFIDENCE_THRESHOLD +
                          (detection.deepfakeScore * (1.0f - FACE_CONFIDENCE_THRESHOLD));

    // Create thumbnail
    cv::Mat thumbnail;
    cv::resize(face, thumbnail, cv::Size(64, 64));
    detection.thumbnail = matToQPixmap(thumbnail);

    return detection;
}
#else
FaceDetection DeepfakeDetector::analyzeFace(const QPixmap &face, int frameIndex)
{
    FaceDetection detection;
    detection.frameIndex = frameIndex;

    // Fallback implementation without OpenCV
    QPixmap resizedFace = face.scaled(FACE_INPUT_SIZE, FACE_INPUT_SIZE, Qt::KeepAspectRatio, Qt::SmoothTransformation);

    // Calculate deepfake score using fallback method
    detection.deepfakeScore = calculateFaceScore(resizedFace);
    detection.confidence = FACE_CONFIDENCE_THRESHOLD +
                          (detection.deepfakeScore * (1.0f - FACE_CONFIDENCE_THRESHOLD));

    // Create thumbnail
    detection.thumbnail = face.scaled(64, 64, Qt::KeepAspectRatio, Qt::SmoothTransformation);

    return detection;
}
#endif

#ifdef HAVE_OPENCV
float DeepfakeDetector::calculateFaceScore(const cv::Mat &face)
{
    // Use ONNX model if available
    float modelScore = runFaceModel(face);
    if (modelScore >= 0) {
        return modelScore;
    }

    // Fallback: basic image analysis
    cv::Mat gray;
    cv::cvtColor(face, gray, cv::COLOR_BGR2GRAY);

    // Calculate various features that might indicate synthetic content
    cv::Scalar mean, stddev;
    cv::meanStdDev(gray, mean, stddev);

    // Edge density
    cv::Mat edges;
    cv::Canny(gray, edges, 50, 150);
    int edgePixels = cv::countNonZero(edges);
    float edgeDensity = static_cast<float>(edgePixels) / (face.rows * face.cols);

    // Texture analysis using LBP-like features
    cv::Mat laplacian;
    cv::Laplacian(gray, laplacian, CV_64F);
    cv::Scalar laplacianMean, laplacianStddev;
    cv::meanStdDev(laplacian, laplacianMean, laplacianStddev);

    // Simple heuristic scoring (this is a placeholder for real ML model)
    float score = 0.0f;

    // High edge density might indicate synthetic artifacts
    if (edgeDensity > 0.15f) score += 0.3f;

    // Unusual variance patterns
    if (stddev[0] < 20 || stddev[0] > 80) score += 0.2f;

    // Laplacian variance (blur detection)
    if (laplacianStddev[0] < 10) score += 0.3f;

    // Add some randomness to simulate model uncertainty
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<float> dis(-0.1f, 0.1f);
    score += dis(gen);

    return qBound(0.0f, score, 1.0f);
}
#else
float DeepfakeDetector::calculateFaceScore(const QPixmap &face)
{
    // Use ONNX model if available
    float modelScore = runFaceModel(face);
    if (modelScore >= 0) {
        return modelScore;
    }

    // Fallback: basic heuristic scoring without OpenCV
    float score = 0.0f;

    // Simple analysis based on image properties
    if (face.width() != face.height()) score += 0.1f; // Aspect ratio check
    if (face.width() < 32 || face.width() > 512) score += 0.2f; // Size check

    // Add some randomness to simulate model uncertainty
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<float> dis(-0.1f, 0.1f);
    score += dis(gen);

    return qBound(0.0f, score, 1.0f);
}
#endif

QVector<AudioSegment> DeepfakeDetector::extractAndAnalyzeAudio(const QString &filePath)
{
    QVector<AudioSegment> segments;

    try {
        float sampleRate;
        QVector<float> audioData = loadAudioData(filePath, sampleRate);

        if (audioData.isEmpty()) {
            return segments;
        }

        // Process audio in segments
        int segmentSamples = static_cast<int>(sampleRate * 2.0f); // 2-second segments
        int numSegments = audioData.size() / segmentSamples;

        for (int i = 0; i < numSegments; i++) {
            int startIdx = i * segmentSamples;
            int endIdx = qMin(startIdx + segmentSamples, audioData.size());

            QVector<float> segmentData = audioData.mid(startIdx, endIdx - startIdx);
            QVector<float> features = extractAudioFeatures(segmentData, sampleRate);

            AudioSegment segment;
            segment.startTime = i * 2.0f;
            segment.endTime = (i + 1) * 2.0f;
            segment.features = features;
            segment.deepfakeScore = calculateAudioScore(features);
            segment.confidence = AUDIO_CONFIDENCE_THRESHOLD +
                               (segment.deepfakeScore * (1.0f - AUDIO_CONFIDENCE_THRESHOLD));

            segments.append(segment);
        }

    } catch (const std::exception& e) {
        qWarning() << "Audio analysis failed:" << e.what();
    }

    return segments;
}

QVector<float> DeepfakeDetector::extractAudioFeatures(const QVector<float> &audioData, float sampleRate)
{
    QVector<float> features;

    if (audioData.isEmpty()) {
        return features;
    }

    // Basic audio features (placeholder for real feature extraction)

    // 1. RMS Energy
    float rms = 0.0f;
    for (float sample : audioData) {
        rms += sample * sample;
    }
    rms = std::sqrt(rms / audioData.size());
    features.append(rms);

    // 2. Zero Crossing Rate
    int zeroCrossings = 0;
    for (int i = 1; i < audioData.size(); i++) {
        if ((audioData[i] >= 0) != (audioData[i-1] >= 0)) {
            zeroCrossings++;
        }
    }
    float zcr = static_cast<float>(zeroCrossings) / audioData.size();
    features.append(zcr);

    // 3. Spectral Centroid (simplified)
    // This is a very basic approximation
    float spectralCentroid = 0.0f;
    for (int i = 0; i < audioData.size(); i++) {
        spectralCentroid += i * std::abs(audioData[i]);
    }
    spectralCentroid /= audioData.size();
    features.append(spectralCentroid / sampleRate);

    // 4. Mean and variance
    float mean = 0.0f;
    for (float sample : audioData) {
        mean += sample;
    }
    mean /= audioData.size();
    features.append(mean);

    float variance = 0.0f;
    for (float sample : audioData) {
        variance += (sample - mean) * (sample - mean);
    }
    variance /= audioData.size();
    features.append(variance);

    return features;
}

float DeepfakeDetector::calculateAudioScore(const QVector<float> &features)
{
    // Use ONNX model if available
    float modelScore = runVoiceModel(features);
    if (modelScore >= 0) {
        return modelScore;
    }

    // Fallback: basic heuristic scoring
    if (features.size() < 5) {
        return 0.0f;
    }

    float rms = features[0];
    float zcr = features[1];
    float spectralCentroid = features[2];
    float mean = features[3];
    float variance = features[4];

    float score = 0.0f;

    // Unusual energy patterns
    if (rms < 0.01f || rms > 0.5f) score += 0.2f;

    // Abnormal zero crossing rate
    if (zcr < 0.01f || zcr > 0.3f) score += 0.2f;

    // Spectral anomalies
    if (spectralCentroid < 0.1f || spectralCentroid > 0.8f) score += 0.2f;

    // Statistical anomalies
    if (variance < 0.001f || variance > 0.1f) score += 0.2f;

    // Add randomness
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<float> dis(-0.1f, 0.1f);
    score += dis(gen);

    return qBound(0.0f, score, 1.0f);
}

QVector<float> DeepfakeDetector::loadAudioData(const QString &filePath, float &sampleRate)
{
    QVector<float> audioData;
    sampleRate = 16000.0f; // Default sample rate

    // This is a placeholder implementation
    // In a real application, you would use a library like:
    // - libsndfile
    // - FFmpeg
    // - OpenCV's audio capabilities
    // - Qt Multimedia

    qWarning() << "Audio loading not fully implemented. Using placeholder data.";

    // Generate placeholder audio data for testing
    int numSamples = static_cast<int>(sampleRate * 10.0f); // 10 seconds
    audioData.reserve(numSamples);

    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<float> dis(-0.1f, 0.1f);

    for (int i = 0; i < numSamples; i++) {
        audioData.append(dis(gen));
    }

    return audioData;
}

#ifdef HAVE_OPENCV
float DeepfakeDetector::runFaceModel(const cv::Mat &face)
{
#ifdef HAVE_ONNXRUNTIME
    if (!m_faceSession) {
        return -1.0f; // Model not available
    }

    try {
        // Prepare input tensor
        cv::Mat floatFace;
        face.convertTo(floatFace, CV_32F, 1.0/255.0);

        std::vector<float> inputData;
        inputData.assign((float*)floatFace.datastart, (float*)floatFace.dataend);

        std::vector<int64_t> inputShape = {1, 3, FACE_INPUT_SIZE, FACE_INPUT_SIZE};

        Ort::Value inputTensor = Ort::Value::CreateTensor<float>(
            m_memoryInfo, inputData.data(), inputData.size(),
            inputShape.data(), inputShape.size());

        // Run inference
        const char* inputNames[] = {"input"};
        const char* outputNames[] = {"output"};

        auto outputTensors = m_faceSession->Run(Ort::RunOptions{nullptr},
                                               inputNames, &inputTensor, 1,
                                               outputNames, 1);

        // Get result
        float* outputData = outputTensors[0].GetTensorMutableData<float>();
        return outputData[0]; // Assuming single output value

    } catch (const std::exception& e) {
        qWarning() << "Face model inference failed:" << e.what();
        return -1.0f;
    }
#endif

    return -1.0f; // ONNX Runtime not available
}
#else
float DeepfakeDetector::runFaceModel(const QPixmap &face)
{
    Q_UNUSED(face)
    return -1.0f; // OpenCV not available
}
#endif

float DeepfakeDetector::runVoiceModel(const QVector<float> &features)
{
#ifdef HAVE_ONNXRUNTIME
    if (!m_voiceSession || features.isEmpty()) {
        return -1.0f;
    }

    try {
        std::vector<float> inputData = features.toStdVector();
        std::vector<int64_t> inputShape = {1, static_cast<int64_t>(features.size())};

        Ort::Value inputTensor = Ort::Value::CreateTensor<float>(
            m_memoryInfo, inputData.data(), inputData.size(),
            inputShape.data(), inputShape.size());

        const char* inputNames[] = {"input"};
        const char* outputNames[] = {"output"};

        auto outputTensors = m_voiceSession->Run(Ort::RunOptions{nullptr},
                                               inputNames, &inputTensor, 1,
                                               outputNames, 1);

        float* outputData = outputTensors[0].GetTensorMutableData<float>();
        return outputData[0];

    } catch (const std::exception& e) {
        qWarning() << "Voice model inference failed:" << e.what();
        return -1.0f;
    }
#endif

    return -1.0f;
}

#ifdef HAVE_OPENCV
QPixmap DeepfakeDetector::matToQPixmap(const cv::Mat &mat)
{
    if (mat.empty()) {
        return QPixmap();
    }

    cv::Mat rgbMat;
    if (mat.channels() == 3) {
        cv::cvtColor(mat, rgbMat, cv::COLOR_BGR2RGB);
    } else if (mat.channels() == 1) {
        cv::cvtColor(mat, rgbMat, cv::COLOR_GRAY2RGB);
    } else {
        rgbMat = mat;
    }

    QImage qimg(rgbMat.data, rgbMat.cols, rgbMat.rows, rgbMat.step, QImage::Format_RGB888);
    return QPixmap::fromImage(qimg);
}

cv::Mat DeepfakeDetector::qPixmapToMat(const QPixmap &pixmap)
{
    QImage qimg = pixmap.toImage().convertToFormat(QImage::Format_RGB888);
    return cv::Mat(qimg.height(), qimg.width(), CV_8UC3, (void*)qimg.constBits(), qimg.bytesPerLine()).clone();
}
#endif // HAVE_OPENCV

QString DeepfakeDetector::getFileType(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    QString suffix = fileInfo.suffix().toLower();

    QStringList videoFormats = {"mp4", "avi", "mov", "mkv", "wmv", "flv", "webm"};
    QStringList audioFormats = {"mp3", "wav", "flac", "aac", "ogg", "m4a"};

    if (videoFormats.contains(suffix)) {
        return "video";
    } else if (audioFormats.contains(suffix)) {
        return "audio";
    } else {
        return "unknown";
    }
}

float DeepfakeDetector::combineScores(float faceScore, float audioScore, float faceWeight)
{
    if (faceScore <= 0.0f && audioScore <= 0.0f) {
        return 0.0f;
    } else if (faceScore <= 0.0f) {
        return audioScore;
    } else if (audioScore <= 0.0f) {
        return faceScore;
    } else {
        return faceScore * faceWeight + audioScore * (1.0f - faceWeight);
    }
}

bool DeepfakeDetector::loadFaceModel()
{
#ifdef HAVE_ONNXRUNTIME
    if (!QFileInfo::exists(m_faceModelPath)) {
        qWarning() << "Face model not found:" << m_faceModelPath;
        return false;
    }

    try {
        Ort::SessionOptions sessionOptions;
        sessionOptions.SetIntraOpNumThreads(1);
        sessionOptions.SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_EXTENDED);

        m_faceSession = std::make_unique<Ort::Session>(*m_ortEnv,
                                                      m_faceModelPath.toStdString().c_str(),
                                                      sessionOptions);

        qDebug() << "Face model loaded successfully";
        return true;

    } catch (const std::exception& e) {
        qWarning() << "Failed to load face model:" << e.what();
        return false;
    }
#endif

    return false;
}

bool DeepfakeDetector::loadVoiceModel()
{
#ifdef HAVE_ONNXRUNTIME
    if (!QFileInfo::exists(m_voiceModelPath)) {
        qWarning() << "Voice model not found:" << m_voiceModelPath;
        return false;
    }

    try {
        Ort::SessionOptions sessionOptions;
        sessionOptions.SetIntraOpNumThreads(1);
        sessionOptions.SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_EXTENDED);

        m_voiceSession = std::make_unique<Ort::Session>(*m_ortEnv,
                                                       m_voiceModelPath.toStdString().c_str(),
                                                       sessionOptions);

        qDebug() << "Voice model loaded successfully";
        return true;

    } catch (const std::exception& e) {
        qWarning() << "Failed to load voice model:" << e.what();
        return false;
    }
#endif

    return false;
}

void DeepfakeDetector::cleanup()
{
#ifdef HAVE_ONNXRUNTIME
    m_faceSession.reset();
    m_voiceSession.reset();
    m_ortEnv.reset();
#endif

    m_initialized = false;
}
