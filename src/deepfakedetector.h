#ifndef DEEPFAKEDETECTOR_H
#define DEEPFAKEDETECTOR_H

#include <QObject>
#include <QString>
#include <QVector>
#include <QPixmap>
#include <QThread>
#include <QMutex>
#include <QTimer>

#ifdef HAVE_OPENCV
#include <opencv2/opencv.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/objdetect.hpp>
#endif

#ifdef HAVE_ONNXRUNTIME
#include <onnxruntime_cxx_api.h>
#endif

struct FaceDetection {
#ifdef HAVE_OPENCV
    cv::Rect boundingBox;
#else
    QRect boundingBox;
#endif
    float confidence;
    float deepfakeScore;
    int frameIndex;
    QPixmap thumbnail;
};

struct AudioSegment {
    float startTime;
    float endTime;
    float deepfakeScore;
    float confidence;
    QVector<float> features;
};

struct DetectionResult {
    QString filePath;
    QString fileType;
    
    // Overall scores
    float overallScore;
    float confidence;
    
    // Face analysis results
    QVector<FaceDetection> faceDetections;
    float faceScore;
    float faceConfidence;
    int totalFrames;
    int processedFrames;
    
    // Audio analysis results
    QVector<AudioSegment> audioSegments;
    float audioScore;
    float audioConfidence;
    float audioDuration;
    
    // Processing metadata
    QString processingTime;
    QString modelVersions;
    QVector<QString> warnings;
    
    // Visual data for display
    QPixmap videoThumbnail;
    QVector<float> audioWaveform;
    QVector<bool> suspiciousFrames; // Frame-by-frame suspicion flags
};

class DeepfakeDetector : public QObject
{
    Q_OBJECT

public:
    explicit DeepfakeDetector(QObject *parent = nullptr);
    ~DeepfakeDetector();

    bool initializeModels();
    bool isInitialized() const { return m_initialized; }

public slots:
    void analyzeFile(const QString &filePath);

signals:
    void progressUpdated(int percentage);
    void detectionComplete(const DetectionResult &result);
    void detectionError(const QString &error);

private:
    // Core analysis methods
    DetectionResult analyzeVideo(const QString &filePath);
    DetectionResult analyzeAudio(const QString &filePath);
    DetectionResult analyzeMixed(const QString &filePath);
    
    // Video processing
#ifdef HAVE_OPENCV
    QVector<FaceDetection> extractAndAnalyzeFaces(cv::VideoCapture &cap, int totalFrames);
    FaceDetection analyzeFace(const cv::Mat &face, int frameIndex);
    QVector<cv::Rect> detectFaces(const cv::Mat &frame);
    float calculateFaceScore(const cv::Mat &face);
#else
    QVector<FaceDetection> extractAndAnalyzeFaces(void* cap, int totalFrames);
    FaceDetection analyzeFace(const QPixmap &face, int frameIndex);
    QVector<QRect> detectFaces(const void* frame);
    float calculateFaceScore(const QPixmap &face);
#endif
    
    // Audio processing
    QVector<AudioSegment> extractAndAnalyzeAudio(const QString &filePath);
    QVector<float> extractAudioFeatures(const QVector<float> &audioData, float sampleRate);
    float calculateAudioScore(const QVector<float> &features);
    QVector<float> loadAudioData(const QString &filePath, float &sampleRate);
    
    // Model inference
#ifdef HAVE_OPENCV
    float runFaceModel(const cv::Mat &face);
#else
    float runFaceModel(const QPixmap &face);
#endif
    float runVoiceModel(const QVector<float> &features);
    
    // Utility methods
#ifdef HAVE_OPENCV
    QPixmap matToQPixmap(const cv::Mat &mat);
    cv::Mat qPixmapToMat(const QPixmap &pixmap);
#endif
    QString getFileType(const QString &filePath);
    float combineScores(float faceScore, float audioScore, float faceWeight = 0.6f);
    
    // Model management
    bool loadFaceModel();
    bool loadVoiceModel();
    void cleanup();

private:
    // Model paths
    QString m_faceModelPath;
    QString m_voiceModelPath;
    
    // OpenCV components
#ifdef HAVE_OPENCV
    cv::CascadeClassifier m_faceCascade;
    cv::HOGDescriptor m_hog;
#endif
    
#ifdef HAVE_ONNXRUNTIME
    // ONNX Runtime components
    std::unique_ptr<Ort::Env> m_ortEnv;
    std::unique_ptr<Ort::Session> m_faceSession;
    std::unique_ptr<Ort::Session> m_voiceSession;
    Ort::MemoryInfo m_memoryInfo;
#endif
    
    // State
    bool m_initialized;
    QMutex m_mutex;
    
    // Configuration
    static constexpr int FACE_INPUT_SIZE = 224;
    static constexpr int AUDIO_SEGMENT_LENGTH = 16000; // 1 second at 16kHz
    static constexpr float FACE_CONFIDENCE_THRESHOLD = 0.5f;
    static constexpr float AUDIO_CONFIDENCE_THRESHOLD = 0.6f;
    static constexpr int MAX_FACES_PER_FRAME = 5;
    static constexpr int FRAME_SKIP_INTERVAL = 30; // Process every 30th frame
};

#endif // DEEPFAKEDETECTOR_H
