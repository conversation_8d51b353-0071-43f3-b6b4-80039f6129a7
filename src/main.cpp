#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>
#include <QMessageBox>
#include <QCommandLineParser>
#include <QCommandLineOption>
#include <iostream>

#include "appwindow.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Set application properties
    app.setApplicationName("Deepfake Predictor");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("DeepfakePredictor");
    app.setOrganizationDomain("deepfakepredictor.com");
    
    // Set up command line parser
    QCommandLineParser parser;
    parser.setApplicationDescription("AI-powered deepfake detection tool for video and audio files");
    parser.addHelpOption();
    parser.addVersionOption();
    
    // Add positional argument for input file
    parser.addPositionalArgument("file", "Video or audio file to analyze (optional)");
    
    // Add options
    QCommandLineOption batchOption(QStringList() << "b" << "batch", 
                                  "Run in batch mode without GUI");
    parser.addOption(batchOption);
    
    QCommandLineOption outputOption(QStringList() << "o" << "output", 
                                   "Output file for batch mode results", "output");
    parser.addOption(outputOption);
    
    // Process command line arguments
    parser.process(app);
    
    // Check if models directory exists
    QString modelsDir = QDir::currentPath() + "/models";
    if (!QDir(modelsDir).exists()) {
        QMessageBox::warning(nullptr, "Models Directory Missing", 
                           "Models directory not found. Please ensure the models directory exists "
                           "and contains the required ONNX model files:\n"
                           "- voice-detector.onnx\n"
                           "- face-detector.onnx\n\n"
                           "See README.md for download instructions.");
    }
    
    // Get positional arguments (input files)
    const QStringList args = parser.positionalArguments();
    QString inputFile;
    if (!args.isEmpty()) {
        inputFile = args.first();
    }
    
    // Check for batch mode
    if (parser.isSet(batchOption)) {
        if (inputFile.isEmpty()) {
            std::cerr << "Error: Input file required for batch mode" << std::endl;
            return 1;
        }
        
        // TODO: Implement batch processing
        std::cout << "Batch mode processing: " << inputFile.toStdString() << std::endl;
        std::cout << "Batch mode not yet implemented. Please use GUI mode." << std::endl;
        return 0;
    }
    
    // Set application style
    app.setStyle(QStyleFactory::create("Fusion"));
    
    // Apply dark theme
    QPalette darkPalette;
    darkPalette.setColor(QPalette::Window, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::WindowText, Qt::white);
    darkPalette.setColor(QPalette::Base, QColor(25, 25, 25));
    darkPalette.setColor(QPalette::AlternateBase, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ToolTipBase, Qt::white);
    darkPalette.setColor(QPalette::ToolTipText, Qt::white);
    darkPalette.setColor(QPalette::Text, Qt::white);
    darkPalette.setColor(QPalette::Button, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ButtonText, Qt::white);
    darkPalette.setColor(QPalette::BrightText, Qt::red);
    darkPalette.setColor(QPalette::Link, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::Highlight, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::HighlightedText, Qt::black);
    app.setPalette(darkPalette);
    
    // Create and show main window
    AppWindow window;
    
    // If input file provided, load it
    if (!inputFile.isEmpty()) {
        window.loadFile(inputFile);
    }
    
    window.show();
    
    return app.exec();
}
