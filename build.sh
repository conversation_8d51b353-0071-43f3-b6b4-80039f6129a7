#!/bin/bash

# Deepfake Predictor Build Script
# This script automates the build process for the Deepfake Predictor application

set -e  # Exit on any error

echo "=== Deepfake Predictor Build Script ==="
echo

# Check if we're in the right directory
if [ ! -f "CMakeLists.txt" ]; then
    echo "Error: CMakeLists.txt not found. Please run this script from the project root directory."
    exit 1
fi

# Create build directory
BUILD_DIR="build"
if [ -d "$BUILD_DIR" ]; then
    echo "Build directory exists. Cleaning..."
    rm -rf "$BUILD_DIR"
fi

mkdir "$BUILD_DIR"
cd "$BUILD_DIR"

echo "Configuring with CMake..."

# Detect system and set appropriate flags
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "Detected Linux system"
    CMAKE_FLAGS=""
elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo "Detected macOS system"
    # Try to find Qt6 via Homebrew
    if [ -d "/opt/homebrew/lib/cmake/Qt6" ]; then
        CMAKE_FLAGS="-DCMAKE_PREFIX_PATH=/opt/homebrew"
    elif [ -d "/usr/local/lib/cmake/Qt6" ]; then
        CMAKE_FLAGS="-DCMAKE_PREFIX_PATH=/usr/local"
    else
        CMAKE_FLAGS=""
    fi
else
    echo "Detected other system (assuming Unix-like)"
    CMAKE_FLAGS=""
fi

# Configure
cmake $CMAKE_FLAGS ..

echo "Building application..."

# Build
if command -v nproc > /dev/null; then
    JOBS=$(nproc)
else
    JOBS=4
fi

make -j$JOBS

echo
echo "=== Build Complete ==="
echo "Executable location: $PWD/DeepfakePredictor"
echo

# Check if models directory exists
if [ ! -d "../models" ]; then
    echo "WARNING: Models directory not found!"
    echo "Please create a 'models' directory and download the required ONNX models:"
    echo "  - face-detector.onnx"
    echo "  - voice-detector.onnx"
    echo "See README.md for download instructions."
    echo
fi

# Check for required libraries
echo "Checking dependencies..."

# Check Qt6
if ! pkg-config --exists Qt6Core; then
    echo "WARNING: Qt6 may not be properly installed or configured"
fi

# Check OpenCV
if ! pkg-config --exists opencv4; then
    echo "WARNING: OpenCV may not be properly installed"
fi

echo
echo "To run the application:"
echo "  cd $BUILD_DIR"
echo "  ./DeepfakePredictor"
echo
echo "Or with a test file:"
echo "  ./DeepfakePredictor /path/to/test/video.mp4"
echo
