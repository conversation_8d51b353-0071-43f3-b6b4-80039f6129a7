#!/bin/bash

# Test script for Deepfake Predictor application

echo "=== Deepfake Predictor Test Script ==="
echo

# Check if application exists
if [ ! -f "build/DeepfakePredictor" ]; then
    echo "Error: Application not found. Please build first using:"
    echo "  ./build.sh"
    exit 1
fi

echo "✓ Application found"

# Check if models directory exists
if [ ! -d "models" ]; then
    echo "Warning: Models directory not found. Creating placeholder models..."
    ./download_models.sh
fi

echo "✓ Models directory ready"

# Create test files
echo "Creating test files..."
mkdir -p test_files

# Create a simple test "video" file (text file with video extension)
echo "This is a test video file for deepfake analysis" > test_files/test_video.mp4

# Create a simple test "audio" file (text file with audio extension)  
echo "This is a test audio file for deepfake analysis" > test_files/test_audio.wav

echo "✓ Test files created"

echo
echo "=== Running Application Tests ==="
echo

# Test 1: Launch application without arguments
echo "Test 1: Launching application GUI..."
echo "Note: The application will open in GUI mode."
echo "You can:"
echo "  1. Drag and drop the test files from test_files/ directory"
echo "  2. Click 'Open File' and select test_files/test_video.mp4"
echo "  3. Click 'Analyze for Deepfakes' to run analysis"
echo "  4. Click 'Save Report' to generate a PDF report"
echo "  5. Close the application when done testing"
echo

echo "Starting application..."
echo "Press Ctrl+C to stop the application when you're done testing."

# Launch the application
cd build
./DeepfakePredictor ../test_files/test_video.mp4
