cmake_minimum_required(VERSION 3.16)
project(DeepfakePredictor VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(Qt6 REQUIRED COMPONENTS Core Widgets PrintSupport)

# Try to find OpenCV
find_package(OpenCV QUIET)
if(NOT OpenCV_FOUND)
    # Try pkg-config as fallback
    find_package(PkgConfig QUIET)
    if(PkgConfig_FOUND)
        pkg_check_modules(OpenCV QUIET opencv4)
        if(NOT OpenCV_FOUND)
            pkg_check_modules(OpenCV QUIET opencv)
        endif()
    endif()
endif()

if(NOT OpenCV_FOUND)
    message(WARNING "OpenCV not found. Some features will be disabled.")
    set(HAVE_OPENCV FALSE)
else()
    message(STATUS "OpenCV found: ${OpenCV_VERSION}")
    set(HAVE_OPENCV TRUE)
endif()

# Optional Qt6 components
find_package(Qt6 QUIET COMPONENTS Quick QuickWidgets)
if(Qt6Quick_FOUND AND Qt6QuickWidgets_FOUND)
    message(STATUS "Qt6 Quick components found - enabling QML support")
    set(HAVE_QT_QUICK TRUE)
else()
    message(STATUS "Qt6 Quick components not found - using Widgets only")
    set(HAVE_QT_QUICK FALSE)
endif()

# Try to find ONNX Runtime
find_path(ONNXRUNTIME_INCLUDE_DIR
    NAMES onnxruntime_cxx_api.h
    PATHS
        /usr/local/include/onnxruntime
        /usr/include/onnxruntime
        ${CMAKE_SOURCE_DIR}/third_party/onnxruntime/include
    PATH_SUFFIXES core/session
)

find_library(ONNXRUNTIME_LIB
    NAMES onnxruntime
    PATHS
        /usr/local/lib
        /usr/lib
        ${CMAKE_SOURCE_DIR}/third_party/onnxruntime/lib
)

if(NOT ONNXRUNTIME_INCLUDE_DIR OR NOT ONNXRUNTIME_LIB)
    message(WARNING "ONNX Runtime not found. Please install it or set paths manually.")
    message(STATUS "You can download ONNX Runtime from: https://github.com/microsoft/onnxruntime/releases")
endif()

# Set up Qt6 (manual setup for older Qt6 versions)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# Source files
set(SOURCES
    src/main.cpp
    src/appwindow.cpp
    src/deepfakedetector.cpp
    src/visualizer.cpp
    src/reportgenerator.cpp
)

set(HEADERS
    src/appwindow.h
    src/deepfakedetector.h
    src/visualizer.h
    src/reportgenerator.h
)

# Create executable
add_executable(DeepfakePredictor ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(DeepfakePredictor
    Qt6::Core
    Qt6::Widgets
    Qt6::PrintSupport
)

# Link OpenCV if available
if(HAVE_OPENCV)
    target_link_libraries(DeepfakePredictor ${OpenCV_LIBS})
    target_compile_definitions(DeepfakePredictor PRIVATE HAVE_OPENCV)
endif()

# Link optional Qt6 Quick components if available
if(HAVE_QT_QUICK)
    target_link_libraries(DeepfakePredictor Qt6::Quick Qt6::QuickWidgets)
    target_compile_definitions(DeepfakePredictor PRIVATE HAVE_QT_QUICK)
endif()

# Add ONNX Runtime if found
if(ONNXRUNTIME_INCLUDE_DIR AND ONNXRUNTIME_LIB)
    target_include_directories(DeepfakePredictor PRIVATE ${ONNXRUNTIME_INCLUDE_DIR})
    target_link_libraries(DeepfakePredictor ${ONNXRUNTIME_LIB})
    target_compile_definitions(DeepfakePredictor PRIVATE HAVE_ONNXRUNTIME)
endif()

# Include directories
target_include_directories(DeepfakePredictor PRIVATE src)

# Add OpenCV include directories if available
if(HAVE_OPENCV)
    target_include_directories(DeepfakePredictor PRIVATE ${OpenCV_INCLUDE_DIRS})
endif()

# Compiler definitions
target_compile_definitions(DeepfakePredictor PRIVATE
    QT_DEPRECATED_WARNINGS
)

# Copy models directory to build directory
file(COPY ${CMAKE_SOURCE_DIR}/models DESTINATION ${CMAKE_BINARY_DIR})

# Install target
install(TARGETS DeepfakePredictor
    BUNDLE DESTINATION .
    RUNTIME DESTINATION bin
)

# Create models directory
file(MAKE_DIRECTORY ${CMAKE_SOURCE_DIR}/models)

# Print build information
message(STATUS "Qt6 version: ${Qt6_VERSION}")
message(STATUS "OpenCV version: ${OpenCV_VERSION}")
if(ONNXRUNTIME_INCLUDE_DIR AND ONNXRUNTIME_LIB)
    message(STATUS "ONNX Runtime found: ${ONNXRUNTIME_LIB}")
else()
    message(STATUS "ONNX Runtime: NOT FOUND")
endif()
