# Deepfake Predictor - Usage Guide

## Quick Start

### 1. Build the Application

```bash
# Make build script executable and run it
chmod +x build.sh
./build.sh
```

### 2. Set Up Models (Optional)

```bash
# Download placeholder models and setup
chmod +x download_models.sh
./download_models.sh
```

### 3. Run the Application

```bash
# Launch GUI
cd build
./DeepfakePredictor

# Or with a file argument
./DeepfakePredictor /path/to/video.mp4
```

## Application Features

### Main Interface

The application provides a modern Qt6 interface with:

- **Left Panel**: File input controls
  - Drag & drop area for media files
  - "Open File" button for file selection
  - File information display
  - "Analyze for Deepfakes" button
  - "Save Report" button

- **Right Panel**: Analysis results
  - Score gauges for Overall, Face, and Audio analysis
  - Progress tracking with detailed logs
  - Visual analysis section with thumbnails and waveforms

### Supported File Formats

- **Video**: MP4, AVI, MOV, MKV, WMV, FLV, WebM
- **Audio**: MP3, WAV, FLAC, AAC, OGG, M4A

### Analysis Process

1. **Load File**: Drag & drop or use "Open File" button
2. **Start Analysis**: Click "Analyze for Deepfakes"
3. **View Results**: Check score gauges and visual analysis
4. **Save Report**: Export results as PDF or screenshot

### Score Interpretation

- **0-30%**: Low likelihood of synthetic content (likely authentic)
- **30-70%**: Moderate likelihood (requires further investigation)
- **70-100%**: High likelihood of deepfake/synthetic content

### Confidence Levels

- **80-100%**: High confidence in analysis
- **60-79%**: Medium confidence
- **40-59%**: Low confidence
- **0-39%**: Very low confidence (results may be unreliable)

## Testing the Application

### Using the Test Script

```bash
# Run comprehensive test
./test_app.sh
```

This will:
1. Check if the application is built
2. Set up models if needed
3. Create test files
4. Launch the application with a test file

### Manual Testing Steps

1. **Launch Application**:
   ```bash
   cd build
   ./DeepfakePredictor
   ```

2. **Test File Loading**:
   - Create a test file: `echo "test" > test.mp4`
   - Drag the file into the application
   - Or use "Open File" button

3. **Test Analysis**:
   - Click "Analyze for Deepfakes"
   - Watch progress indicators
   - Review results in score gauges

4. **Test Report Generation**:
   - Click "Save Report"
   - Choose PDF or PNG format
   - Verify file is created

## Current Implementation Status

### ✅ Completed Features

- **Core Application Structure**: Qt6-based GUI with modern interface
- **File Input System**: Drag & drop and file dialog support
- **Analysis Engine**: Multi-modal detection framework
- **Visualization System**: Score gauges, thumbnails, waveforms, timeline
- **Report Generation**: PDF and image export functionality
- **Build System**: CMake with cross-platform support
- **Fallback Support**: Works without OpenCV/ONNX Runtime

### ⚠️ Placeholder Components

- **ONNX Models**: Currently using placeholder files
- **Audio Processing**: Basic feature extraction (needs real audio libraries)
- **OpenCV Integration**: Optional dependency with fallbacks

### 🔧 For Production Use

To make this production-ready, you would need to:

1. **Replace Placeholder Models**:
   - Train or obtain real deepfake detection models
   - Ensure models match expected input/output formats
   - Update model paths in configuration

2. **Enhance Audio Processing**:
   - Integrate proper audio libraries (libsndfile, FFmpeg)
   - Implement real audio feature extraction
   - Add spectral analysis capabilities

3. **Improve Detection Algorithms**:
   - Implement state-of-the-art detection methods
   - Add ensemble model support
   - Enhance confidence scoring

## Architecture Overview

### Core Components

- **AppWindow**: Main UI and user interaction
- **DeepfakeDetector**: Analysis engine and model management
- **Visualizer**: Results display and interactive elements
- **ReportGenerator**: PDF and image export functionality

### Design Patterns

- **Model-View Architecture**: Clean separation of data and UI
- **Plugin System**: Extensible detection algorithms
- **Fallback Support**: Graceful degradation without dependencies

### Threading Model

- **Main Thread**: UI and user interaction
- **Worker Thread**: Analysis processing
- **Progress Updates**: Real-time feedback to user

## Troubleshooting

### Build Issues

- **Qt6 not found**: Install Qt6 development packages
- **OpenCV missing**: Install libopencv-dev or build without OpenCV
- **ONNX Runtime**: Optional dependency, can be disabled

### Runtime Issues

- **Models not found**: Run `./download_models.sh` or check models/ directory
- **File format errors**: Ensure file format is supported
- **Analysis fails**: Check log output for detailed error messages

### Performance Issues

- **Slow analysis**: Use SSD storage, ensure adequate RAM
- **High CPU usage**: Normal during analysis, reduce thread count if needed
- **Memory usage**: Large videos may require significant RAM

## Development

### Adding New Detection Methods

1. Extend `DeepfakeDetector` class
2. Add new analysis methods
3. Update UI to display new results
4. Add configuration options

### Extending File Format Support

1. Update file type detection in `getFileType()`
2. Add format-specific processing
3. Update UI file filters
4. Test with sample files

### Customizing the Interface

1. Modify `AppWindow` layout
2. Add new visualization components
3. Update stylesheets for theming
4. Test across different screen sizes

## License and Disclaimer

This application is provided for educational and research purposes. Results should be verified by human experts for critical applications. The accuracy depends on the quality of the trained models and input media.
