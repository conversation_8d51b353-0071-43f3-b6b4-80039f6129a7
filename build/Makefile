# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = "/home/<USER>/Desktop/Deepfake predictor"

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = "/home/<USER>/Desktop/Deepfake predictor/build"

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start "/home/<USER>/Desktop/Deepfake predictor/build/CMakeFiles" "/home/<USER>/Desktop/Deepfake predictor/build//CMakeFiles/progress.marks"
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start "/home/<USER>/Desktop/Deepfake predictor/build/CMakeFiles" 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named DeepfakePredictor

# Build rule for target.
DeepfakePredictor: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 DeepfakePredictor
.PHONY : DeepfakePredictor

# fast build rule for target.
DeepfakePredictor/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/build
.PHONY : DeepfakePredictor/fast

#=============================================================================
# Target rules for targets named DeepfakePredictor_autogen

# Build rule for target.
DeepfakePredictor_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 DeepfakePredictor_autogen
.PHONY : DeepfakePredictor_autogen

# fast build rule for target.
DeepfakePredictor_autogen/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor_autogen.dir/build.make CMakeFiles/DeepfakePredictor_autogen.dir/build
.PHONY : DeepfakePredictor_autogen/fast

DeepfakePredictor_autogen/mocs_compilation.o: DeepfakePredictor_autogen/mocs_compilation.cpp.o
.PHONY : DeepfakePredictor_autogen/mocs_compilation.o

# target to build an object file
DeepfakePredictor_autogen/mocs_compilation.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.o
.PHONY : DeepfakePredictor_autogen/mocs_compilation.cpp.o

DeepfakePredictor_autogen/mocs_compilation.i: DeepfakePredictor_autogen/mocs_compilation.cpp.i
.PHONY : DeepfakePredictor_autogen/mocs_compilation.i

# target to preprocess a source file
DeepfakePredictor_autogen/mocs_compilation.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.i
.PHONY : DeepfakePredictor_autogen/mocs_compilation.cpp.i

DeepfakePredictor_autogen/mocs_compilation.s: DeepfakePredictor_autogen/mocs_compilation.cpp.s
.PHONY : DeepfakePredictor_autogen/mocs_compilation.s

# target to generate assembly for a file
DeepfakePredictor_autogen/mocs_compilation.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.s
.PHONY : DeepfakePredictor_autogen/mocs_compilation.cpp.s

src/appwindow.o: src/appwindow.cpp.o
.PHONY : src/appwindow.o

# target to build an object file
src/appwindow.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.o
.PHONY : src/appwindow.cpp.o

src/appwindow.i: src/appwindow.cpp.i
.PHONY : src/appwindow.i

# target to preprocess a source file
src/appwindow.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.i
.PHONY : src/appwindow.cpp.i

src/appwindow.s: src/appwindow.cpp.s
.PHONY : src/appwindow.s

# target to generate assembly for a file
src/appwindow.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.s
.PHONY : src/appwindow.cpp.s

src/deepfakedetector.o: src/deepfakedetector.cpp.o
.PHONY : src/deepfakedetector.o

# target to build an object file
src/deepfakedetector.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.o
.PHONY : src/deepfakedetector.cpp.o

src/deepfakedetector.i: src/deepfakedetector.cpp.i
.PHONY : src/deepfakedetector.i

# target to preprocess a source file
src/deepfakedetector.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.i
.PHONY : src/deepfakedetector.cpp.i

src/deepfakedetector.s: src/deepfakedetector.cpp.s
.PHONY : src/deepfakedetector.s

# target to generate assembly for a file
src/deepfakedetector.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.s
.PHONY : src/deepfakedetector.cpp.s

src/main.o: src/main.cpp.o
.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/reportgenerator.o: src/reportgenerator.cpp.o
.PHONY : src/reportgenerator.o

# target to build an object file
src/reportgenerator.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.o
.PHONY : src/reportgenerator.cpp.o

src/reportgenerator.i: src/reportgenerator.cpp.i
.PHONY : src/reportgenerator.i

# target to preprocess a source file
src/reportgenerator.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.i
.PHONY : src/reportgenerator.cpp.i

src/reportgenerator.s: src/reportgenerator.cpp.s
.PHONY : src/reportgenerator.s

# target to generate assembly for a file
src/reportgenerator.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.s
.PHONY : src/reportgenerator.cpp.s

src/visualizer.o: src/visualizer.cpp.o
.PHONY : src/visualizer.o

# target to build an object file
src/visualizer.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.o
.PHONY : src/visualizer.cpp.o

src/visualizer.i: src/visualizer.cpp.i
.PHONY : src/visualizer.i

# target to preprocess a source file
src/visualizer.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.i
.PHONY : src/visualizer.cpp.i

src/visualizer.s: src/visualizer.cpp.s
.PHONY : src/visualizer.s

# target to generate assembly for a file
src/visualizer.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.s
.PHONY : src/visualizer.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... DeepfakePredictor_autogen"
	@echo "... DeepfakePredictor"
	@echo "... DeepfakePredictor_autogen/mocs_compilation.o"
	@echo "... DeepfakePredictor_autogen/mocs_compilation.i"
	@echo "... DeepfakePredictor_autogen/mocs_compilation.s"
	@echo "... src/appwindow.o"
	@echo "... src/appwindow.i"
	@echo "... src/appwindow.s"
	@echo "... src/deepfakedetector.o"
	@echo "... src/deepfakedetector.i"
	@echo "... src/deepfakedetector.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
	@echo "... src/reportgenerator.o"
	@echo "... src/reportgenerator.i"
	@echo "... src/reportgenerator.s"
	@echo "... src/visualizer.o"
	@echo "... src/visualizer.i"
	@echo "... src/visualizer.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

