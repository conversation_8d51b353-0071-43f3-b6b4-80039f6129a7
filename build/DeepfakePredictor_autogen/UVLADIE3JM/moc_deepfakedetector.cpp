/****************************************************************************
** Meta object code from reading C++ file 'deepfakedetector.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.2.4)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/deepfakedetector.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'deepfakedetector.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.2.4. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_DeepfakeDetector_t {
    const uint offsetsAndSize[22];
    char stringdata0[128];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(offsetof(qt_meta_stringdata_DeepfakeDetector_t, stringdata0) + ofs), len 
static const qt_meta_stringdata_DeepfakeDetector_t qt_meta_stringdata_DeepfakeDetector = {
    {
QT_MOC_LITERAL(0, 16), // "DeepfakeDetector"
QT_MOC_LITERAL(17, 15), // "progressUpdated"
QT_MOC_LITERAL(33, 0), // ""
QT_MOC_LITERAL(34, 10), // "percentage"
QT_MOC_LITERAL(45, 17), // "detectionComplete"
QT_MOC_LITERAL(63, 15), // "DetectionResult"
QT_MOC_LITERAL(79, 6), // "result"
QT_MOC_LITERAL(86, 14), // "detectionError"
QT_MOC_LITERAL(101, 5), // "error"
QT_MOC_LITERAL(107, 11), // "analyzeFile"
QT_MOC_LITERAL(119, 8) // "filePath"

    },
    "DeepfakeDetector\0progressUpdated\0\0"
    "percentage\0detectionComplete\0"
    "DetectionResult\0result\0detectionError\0"
    "error\0analyzeFile\0filePath"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_DeepfakeDetector[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
       4,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    1,   38,    2, 0x06,    1 /* Public */,
       4,    1,   41,    2, 0x06,    3 /* Public */,
       7,    1,   44,    2, 0x06,    5 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       9,    1,   47,    2, 0x0a,    7 /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::Int,    3,
    QMetaType::Void, 0x80000000 | 5,    6,
    QMetaType::Void, QMetaType::QString,    8,

 // slots: parameters
    QMetaType::Void, QMetaType::QString,   10,

       0        // eod
};

void DeepfakeDetector::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<DeepfakeDetector *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->progressUpdated((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 1: _t->detectionComplete((*reinterpret_cast< std::add_pointer_t<DetectionResult>>(_a[1]))); break;
        case 2: _t->detectionError((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->analyzeFile((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (DeepfakeDetector::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DeepfakeDetector::progressUpdated)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (DeepfakeDetector::*)(const DetectionResult & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DeepfakeDetector::detectionComplete)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (DeepfakeDetector::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DeepfakeDetector::detectionError)) {
                *result = 2;
                return;
            }
        }
    }
}

const QMetaObject DeepfakeDetector::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_DeepfakeDetector.offsetsAndSize,
    qt_meta_data_DeepfakeDetector,
    qt_static_metacall,
    nullptr,
qt_incomplete_metaTypeArray<qt_meta_stringdata_DeepfakeDetector_t
, QtPrivate::TypeAndForceComplete<DeepfakeDetector, std::true_type>, QtPrivate::TypeAndForceComplete<void, std::false_type>, QtPrivate::TypeAndForceComplete<int, std::false_type>, QtPrivate::TypeAndForceComplete<void, std::false_type>, QtPrivate::TypeAndForceComplete<const DetectionResult &, std::false_type>, QtPrivate::TypeAndForceComplete<void, std::false_type>, QtPrivate::TypeAndForceComplete<const QString &, std::false_type>
, QtPrivate::TypeAndForceComplete<void, std::false_type>, QtPrivate::TypeAndForceComplete<const QString &, std::false_type>


>,
    nullptr
} };


const QMetaObject *DeepfakeDetector::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *DeepfakeDetector::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_DeepfakeDetector.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int DeepfakeDetector::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void DeepfakeDetector::progressUpdated(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void DeepfakeDetector::detectionComplete(const DetectionResult & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void DeepfakeDetector::detectionError(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
