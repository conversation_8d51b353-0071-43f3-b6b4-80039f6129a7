# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = "/home/<USER>/Desktop/Deepfake predictor"

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = "/home/<USER>/Desktop/Deepfake predictor/build"

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/DeepfakePredictor.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/DeepfakePredictor.dir/clean
clean: CMakeFiles/DeepfakePredictor_autogen.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/DeepfakePredictor.dir

# All Build rule for target.
CMakeFiles/DeepfakePredictor.dir/all: CMakeFiles/DeepfakePredictor_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir="/home/<USER>/Desktop/Deepfake predictor/build/CMakeFiles" --progress-num=1,2,3,4,5,6,7 "Built target DeepfakePredictor"
.PHONY : CMakeFiles/DeepfakePredictor.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DeepfakePredictor.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start "/home/<USER>/Desktop/Deepfake predictor/build/CMakeFiles" 8
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DeepfakePredictor.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start "/home/<USER>/Desktop/Deepfake predictor/build/CMakeFiles" 0
.PHONY : CMakeFiles/DeepfakePredictor.dir/rule

# Convenience name for target.
DeepfakePredictor: CMakeFiles/DeepfakePredictor.dir/rule
.PHONY : DeepfakePredictor

# clean rule for target.
CMakeFiles/DeepfakePredictor.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor.dir/build.make CMakeFiles/DeepfakePredictor.dir/clean
.PHONY : CMakeFiles/DeepfakePredictor.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DeepfakePredictor_autogen.dir

# All Build rule for target.
CMakeFiles/DeepfakePredictor_autogen.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor_autogen.dir/build.make CMakeFiles/DeepfakePredictor_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor_autogen.dir/build.make CMakeFiles/DeepfakePredictor_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir="/home/<USER>/Desktop/Deepfake predictor/build/CMakeFiles" --progress-num=8 "Built target DeepfakePredictor_autogen"
.PHONY : CMakeFiles/DeepfakePredictor_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DeepfakePredictor_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start "/home/<USER>/Desktop/Deepfake predictor/build/CMakeFiles" 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DeepfakePredictor_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start "/home/<USER>/Desktop/Deepfake predictor/build/CMakeFiles" 0
.PHONY : CMakeFiles/DeepfakePredictor_autogen.dir/rule

# Convenience name for target.
DeepfakePredictor_autogen: CMakeFiles/DeepfakePredictor_autogen.dir/rule
.PHONY : DeepfakePredictor_autogen

# clean rule for target.
CMakeFiles/DeepfakePredictor_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DeepfakePredictor_autogen.dir/build.make CMakeFiles/DeepfakePredictor_autogen.dir/clean
.PHONY : CMakeFiles/DeepfakePredictor_autogen.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

