CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.o: \
 /home/<USER>/Desktop/Deepfake\ predictor/src/visualizer.cpp \
 /usr/include/stdc-predef.h \
 /home/<USER>/Desktop/Deepfake\ predictor/src/visualizer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/QWidget \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/qwidget.h \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/qtwidgetsglobal.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qtguiglobal.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qglobal.h \
 /usr/include/c++/11/type_traits \
 /usr/include/x86_64-linux-gnu/c++/11/bits/c++config.h \
 /usr/include/x86_64-linux-gnu/c++/11/bits/os_defines.h \
 /usr/include/features.h /usr/include/features-time64.h \
 /usr/include/x86_64-linux-gnu/bits/wordsize.h \
 /usr/include/x86_64-linux-gnu/bits/timesize.h \
 /usr/include/x86_64-linux-gnu/sys/cdefs.h \
 /usr/include/x86_64-linux-gnu/bits/long-double.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h \
 /usr/include/x86_64-linux-gnu/c++/11/bits/cpu_defines.h \
 /usr/include/c++/11/pstl/pstl_config.h /usr/include/c++/11/cstddef \
 /usr/lib/gcc/x86_64-linux-gnu/11/include/stddef.h \
 /usr/include/c++/11/utility /usr/include/c++/11/bits/stl_relops.h \
 /usr/include/c++/11/bits/stl_pair.h /usr/include/c++/11/bits/move.h \
 /usr/include/c++/11/initializer_list /usr/include/c++/11/cstdint \
 /usr/lib/gcc/x86_64-linux-gnu/11/include/stdint.h /usr/include/stdint.h \
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h \
 /usr/include/x86_64-linux-gnu/bits/types.h \
 /usr/include/x86_64-linux-gnu/bits/typesizes.h \
 /usr/include/x86_64-linux-gnu/bits/time64.h \
 /usr/include/x86_64-linux-gnu/bits/wchar.h \
 /usr/include/x86_64-linux-gnu/bits/stdint-intn.h \
 /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h /usr/include/assert.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qconfig.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtcore-config.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsystemdetection.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qprocessordetection.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcompilerdetection.h \
 /usr/include/c++/11/algorithm /usr/include/c++/11/bits/stl_algobase.h \
 /usr/include/c++/11/bits/functexcept.h \
 /usr/include/c++/11/bits/exception_defines.h \
 /usr/include/c++/11/bits/cpp_type_traits.h \
 /usr/include/c++/11/ext/type_traits.h \
 /usr/include/c++/11/ext/numeric_traits.h \
 /usr/include/c++/11/bits/stl_iterator_base_types.h \
 /usr/include/c++/11/bits/stl_iterator_base_funcs.h \
 /usr/include/c++/11/bits/concept_check.h \
 /usr/include/c++/11/debug/assertions.h \
 /usr/include/c++/11/bits/stl_iterator.h \
 /usr/include/c++/11/bits/ptr_traits.h /usr/include/c++/11/debug/debug.h \
 /usr/include/c++/11/bits/predefined_ops.h \
 /usr/include/c++/11/bits/stl_algo.h /usr/include/c++/11/cstdlib \
 /usr/include/stdlib.h /usr/include/x86_64-linux-gnu/bits/waitflags.h \
 /usr/include/x86_64-linux-gnu/bits/waitstatus.h \
 /usr/include/x86_64-linux-gnu/bits/floatn.h \
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h \
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h \
 /usr/include/x86_64-linux-gnu/sys/types.h \
 /usr/include/x86_64-linux-gnu/bits/types/clock_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/time_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/timer_t.h /usr/include/endian.h \
 /usr/include/x86_64-linux-gnu/bits/endian.h \
 /usr/include/x86_64-linux-gnu/bits/endianness.h \
 /usr/include/x86_64-linux-gnu/bits/byteswap.h \
 /usr/include/x86_64-linux-gnu/bits/uintn-identity.h \
 /usr/include/x86_64-linux-gnu/sys/select.h \
 /usr/include/x86_64-linux-gnu/bits/select.h \
 /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h \
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h \
 /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h \
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h \
 /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h \
 /usr/include/x86_64-linux-gnu/bits/struct_mutex.h \
 /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h /usr/include/alloca.h \
 /usr/include/x86_64-linux-gnu/bits/stdlib-float.h \
 /usr/include/c++/11/bits/std_abs.h \
 /usr/include/c++/11/bits/algorithmfwd.h \
 /usr/include/c++/11/bits/stl_heap.h \
 /usr/include/c++/11/bits/stl_tempbuf.h \
 /usr/include/c++/11/bits/stl_construct.h /usr/include/c++/11/new \
 /usr/include/c++/11/bits/exception.h \
 /usr/include/c++/11/bits/uniform_int_dist.h \
 /usr/include/c++/11/pstl/glue_algorithm_defs.h \
 /usr/include/c++/11/functional /usr/include/c++/11/bits/stl_function.h \
 /usr/include/c++/11/backward/binders.h /usr/include/c++/11/tuple \
 /usr/include/c++/11/array /usr/include/c++/11/bits/range_access.h \
 /usr/include/c++/11/bits/uses_allocator.h \
 /usr/include/c++/11/bits/invoke.h \
 /usr/include/c++/11/bits/functional_hash.h \
 /usr/include/c++/11/bits/hash_bytes.h /usr/include/c++/11/bits/refwrap.h \
 /usr/include/c++/11/bits/std_function.h /usr/include/c++/11/typeinfo \
 /usr/include/c++/11/unordered_map /usr/include/c++/11/bits/allocator.h \
 /usr/include/x86_64-linux-gnu/c++/11/bits/c++allocator.h \
 /usr/include/c++/11/ext/new_allocator.h \
 /usr/include/c++/11/bits/memoryfwd.h \
 /usr/include/c++/11/ext/alloc_traits.h \
 /usr/include/c++/11/bits/alloc_traits.h \
 /usr/include/c++/11/ext/aligned_buffer.h \
 /usr/include/c++/11/bits/hashtable.h \
 /usr/include/c++/11/bits/hashtable_policy.h \
 /usr/include/c++/11/bits/enable_special_members.h \
 /usr/include/c++/11/bits/node_handle.h \
 /usr/include/c++/11/bits/unordered_map.h \
 /usr/include/c++/11/bits/erase_if.h /usr/include/c++/11/vector \
 /usr/include/c++/11/bits/stl_uninitialized.h \
 /usr/include/c++/11/bits/stl_vector.h \
 /usr/include/c++/11/bits/stl_bvector.h \
 /usr/include/c++/11/bits/vector.tcc \
 /usr/include/c++/11/pstl/execution_defs.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtypeinfo.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerfwd.h \
 /usr/include/c++/11/variant /usr/include/c++/11/bits/parse_numbers.h \
 /usr/include/c++/11/optional /usr/include/c++/11/exception \
 /usr/include/c++/11/bits/exception_ptr.h \
 /usr/include/c++/11/bits/cxxabi_init_exception.h \
 /usr/include/c++/11/bits/nested_exception.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsysinfo.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qlogging.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qflags.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbasicatomic.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic_cxx11.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qgenericatomic.h \
 /usr/include/c++/11/atomic /usr/include/c++/11/bits/atomic_base.h \
 /usr/include/c++/11/bits/atomic_lockfree_defines.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qglobalstatic.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qnumeric.h \
 /usr/include/c++/11/cmath /usr/include/math.h \
 /usr/include/x86_64-linux-gnu/bits/math-vector.h \
 /usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h \
 /usr/include/x86_64-linux-gnu/bits/flt-eval-method.h \
 /usr/include/x86_64-linux-gnu/bits/fp-logb.h \
 /usr/include/x86_64-linux-gnu/bits/fp-fast.h \
 /usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h \
 /usr/include/x86_64-linux-gnu/bits/mathcalls.h \
 /usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h \
 /usr/include/x86_64-linux-gnu/bits/iscanonical.h \
 /usr/include/c++/11/bits/specfun.h /usr/include/c++/11/limits \
 /usr/include/c++/11/tr1/gamma.tcc \
 /usr/include/c++/11/tr1/special_function_util.h \
 /usr/include/c++/11/tr1/bessel_function.tcc \
 /usr/include/c++/11/tr1/beta_function.tcc \
 /usr/include/c++/11/tr1/ell_integral.tcc \
 /usr/include/c++/11/tr1/exp_integral.tcc \
 /usr/include/c++/11/tr1/hypergeometric.tcc \
 /usr/include/c++/11/tr1/legendre_function.tcc \
 /usr/include/c++/11/tr1/modified_bessel_func.tcc \
 /usr/include/c++/11/tr1/poly_hermite.tcc \
 /usr/include/c++/11/tr1/poly_laguerre.tcc \
 /usr/include/c++/11/tr1/riemann_zeta.tcc \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qversiontagging.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qtgui-config.h \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/qtwidgets-config.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qwindowdefs.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qnamespace.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtmetamacros.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs_impl.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qobject.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstring.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qchar.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringview.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearray.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qrefcount.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydata.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qpair.h /usr/include/string.h \
 /usr/include/strings.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydatapointer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydataops.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainertools_impl.h \
 /usr/include/c++/11/cstring /usr/include/c++/11/iterator \
 /usr/include/c++/11/iosfwd /usr/include/c++/11/bits/stringfwd.h \
 /usr/include/c++/11/bits/postypes.h /usr/include/c++/11/cwchar \
 /usr/include/wchar.h /usr/lib/gcc/x86_64-linux-gnu/11/include/stdarg.h \
 /usr/include/x86_64-linux-gnu/bits/types/wint_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h \
 /usr/include/c++/11/bits/stream_iterator.h \
 /usr/include/c++/11/bits/streambuf_iterator.h \
 /usr/include/c++/11/streambuf /usr/include/c++/11/bits/localefwd.h \
 /usr/include/x86_64-linux-gnu/c++/11/bits/c++locale.h \
 /usr/include/c++/11/clocale /usr/include/locale.h \
 /usr/include/x86_64-linux-gnu/bits/locale.h /usr/include/c++/11/cctype \
 /usr/include/ctype.h /usr/include/c++/11/bits/ios_base.h \
 /usr/include/c++/11/ext/atomicity.h \
 /usr/include/x86_64-linux-gnu/c++/11/bits/gthr.h \
 /usr/include/x86_64-linux-gnu/c++/11/bits/gthr-default.h \
 /usr/include/pthread.h /usr/include/sched.h \
 /usr/include/x86_64-linux-gnu/bits/sched.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h \
 /usr/include/x86_64-linux-gnu/bits/cpu-set.h /usr/include/time.h \
 /usr/include/x86_64-linux-gnu/bits/time.h \
 /usr/include/x86_64-linux-gnu/bits/timex.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h \
 /usr/include/x86_64-linux-gnu/bits/setjmp.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h \
 /usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h \
 /usr/include/x86_64-linux-gnu/c++/11/bits/atomic_word.h \
 /usr/include/x86_64-linux-gnu/sys/single_threaded.h \
 /usr/include/c++/11/bits/locale_classes.h /usr/include/c++/11/string \
 /usr/include/c++/11/bits/char_traits.h \
 /usr/include/c++/11/bits/ostream_insert.h \
 /usr/include/c++/11/bits/cxxabi_forced.h \
 /usr/include/c++/11/bits/basic_string.h /usr/include/c++/11/string_view \
 /usr/include/c++/11/bits/string_view.tcc \
 /usr/include/c++/11/ext/string_conversions.h /usr/include/c++/11/cstdio \
 /usr/include/stdio.h /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h \
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h \
 /usr/include/c++/11/cerrno /usr/include/errno.h \
 /usr/include/x86_64-linux-gnu/bits/errno.h /usr/include/linux/errno.h \
 /usr/include/x86_64-linux-gnu/asm/errno.h \
 /usr/include/asm-generic/errno.h /usr/include/asm-generic/errno-base.h \
 /usr/include/x86_64-linux-gnu/bits/types/error_t.h \
 /usr/include/c++/11/bits/charconv.h \
 /usr/include/c++/11/bits/basic_string.tcc \
 /usr/include/c++/11/bits/locale_classes.tcc \
 /usr/include/c++/11/system_error \
 /usr/include/x86_64-linux-gnu/c++/11/bits/error_constants.h \
 /usr/include/c++/11/stdexcept /usr/include/c++/11/bits/streambuf.tcc \
 /usr/include/c++/11/memory \
 /usr/include/c++/11/bits/stl_raw_storage_iter.h \
 /usr/include/c++/11/bits/align.h /usr/include/c++/11/bit \
 /usr/include/c++/11/bits/unique_ptr.h \
 /usr/include/c++/11/bits/shared_ptr.h \
 /usr/include/c++/11/bits/shared_ptr_base.h \
 /usr/include/c++/11/bits/allocated_ptr.h \
 /usr/include/c++/11/ext/concurrence.h \
 /usr/include/c++/11/bits/shared_ptr_atomic.h \
 /usr/include/c++/11/backward/auto_ptr.h \
 /usr/include/c++/11/pstl/glue_memory_defs.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayalgorithms.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayview.h \
 /usr/include/c++/11/stdlib.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringliteral.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringalgorithms.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qanystringview.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qutf8stringview.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringtokenizer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qlist.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qhashfunctions.h \
 /usr/include/c++/11/numeric /usr/include/c++/11/bits/stl_numeric.h \
 /usr/include/c++/11/pstl/glue_numeric_defs.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qiterator.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearraylist.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringlist.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qalgorithms.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringmatcher.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qscopedpointer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmetatype.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qvarlengtharray.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qscopeguard.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qdatastream.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qiodevicebase.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qiterable.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmetacontainer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerinfo.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtaggedpointer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmath.h \
 /usr/include/c++/11/list /usr/include/c++/11/bits/stl_list.h \
 /usr/include/c++/11/bits/list.tcc /usr/include/c++/11/map \
 /usr/include/c++/11/bits/stl_tree.h /usr/include/c++/11/bits/stl_map.h \
 /usr/include/c++/11/bits/stl_multimap.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qobject_impl.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbindingstorage.h \
 /usr/include/c++/11/chrono /usr/include/c++/11/ratio \
 /usr/include/c++/11/ctime \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmargins.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qpaintdevice.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qrect.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsize.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qpoint.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qpalette.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qcolor.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qrgb.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qrgba64.h \
 /usr/lib/gcc/x86_64-linux-gnu/11/include/limits.h \
 /usr/lib/gcc/x86_64-linux-gnu/11/include/syslimits.h \
 /usr/include/limits.h /usr/include/x86_64-linux-gnu/bits/posix1_lim.h \
 /usr/include/x86_64-linux-gnu/bits/local_lim.h \
 /usr/include/linux/limits.h \
 /usr/include/x86_64-linux-gnu/bits/posix2_lim.h \
 /usr/include/x86_64-linux-gnu/bits/xopen_lim.h \
 /usr/include/x86_64-linux-gnu/bits/uio_lim.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qbrush.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qimage.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qpixelformat.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qtransform.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qpolygon.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qregion.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qline.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qpixmap.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsharedpointer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qshareddata.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsharedpointer_impl.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qfont.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qfontmetrics.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qfontinfo.h \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/qsizepolicy.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qcursor.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qbitmap.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qkeysequence.h \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/QVBoxLayout \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/qboxlayout.h \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/qlayout.h \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/qlayoutitem.h \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/qboxlayout.h \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/qgridlayout.h \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/QHBoxLayout \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/QGridLayout \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/qgridlayout.h \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/QLabel \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/qlabel.h \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/qframe.h \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/qwidget.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qpicture.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qiodevice.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qtextdocument.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qvariant.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmap.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qshareddata_impl.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qhash.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qdebug.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtextstream.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qset.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcontiguouscache.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qurl.h \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/QScrollArea \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/qscrollarea.h \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/qabstractscrollarea.h \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/QFrame \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/qframe.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/QPainter \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qpainter.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qtextoption.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qpen.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/QPixmap \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qpixmap.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QTimer \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtimer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbasictimer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QPropertyAnimation \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qpropertyanimation.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qvariantanimation.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qabstractanimation.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qeasingcurve.h \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/QGraphicsEffect \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/qgraphicseffect.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/QMouseEvent \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qevent.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcoreevent.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qpointer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qeventpoint.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qvector2d.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qvectornd.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qpointingdevice.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qinputdevice.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qscreen.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QList \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qlist.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QObject \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qobject.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QRect \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qrect.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QSize \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsize.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QSizeF \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/QTransform \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qtransform.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qnativeinterface.h \
 /home/<USER>/Desktop/Deepfake\ predictor/src/deepfakedetector.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QObject \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QString \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstring.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QVector \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qvector.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QThread \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qthread.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qdeadlinetimer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qelapsedtimer.h \
 /usr/include/c++/11/future /usr/include/c++/11/mutex \
 /usr/include/c++/11/bits/std_mutex.h \
 /usr/include/c++/11/bits/unique_lock.h \
 /usr/include/c++/11/condition_variable \
 /usr/include/c++/11/bits/atomic_futex.h \
 /usr/include/c++/11/bits/std_thread.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QMutex \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmutex.h \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/QApplication \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/qapplication.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcoreapplication.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qeventloop.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcoreapplication_platform.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qguiapplication.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qinputmethod.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qlocale.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qguiapplication_platform.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/QPainterPath \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qpainterpath.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/QLinearGradient \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qbrush.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/QRadialGradient \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/QFontMetrics \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qfontmetrics.h \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/QToolTip \
 /usr/include/x86_64-linux-gnu/qt6/QtWidgets/qtooltip.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QFileInfo \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfileinfo.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfile.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfiledevice.h \
 /usr/include/c++/11/filesystem /usr/include/c++/11/bits/fs_fwd.h \
 /usr/include/c++/11/bits/fs_path.h /usr/include/c++/11/locale \
 /usr/include/c++/11/bits/locale_facets.h /usr/include/c++/11/cwctype \
 /usr/include/wctype.h /usr/include/x86_64-linux-gnu/bits/wctype-wchar.h \
 /usr/include/x86_64-linux-gnu/c++/11/bits/ctype_base.h \
 /usr/include/x86_64-linux-gnu/c++/11/bits/ctype_inline.h \
 /usr/include/c++/11/bits/locale_facets.tcc \
 /usr/include/c++/11/bits/locale_facets_nonio.h \
 /usr/include/x86_64-linux-gnu/c++/11/bits/time_members.h \
 /usr/include/x86_64-linux-gnu/c++/11/bits/messages_members.h \
 /usr/include/libintl.h /usr/include/c++/11/bits/codecvt.h \
 /usr/include/c++/11/bits/locale_facets_nonio.tcc \
 /usr/include/c++/11/bits/locale_conv.h /usr/include/c++/11/iomanip \
 /usr/include/c++/11/bits/quoted_string.h /usr/include/c++/11/sstream \
 /usr/include/c++/11/istream /usr/include/c++/11/ios \
 /usr/include/c++/11/bits/basic_ios.h \
 /usr/include/c++/11/bits/basic_ios.tcc /usr/include/c++/11/ostream \
 /usr/include/c++/11/bits/ostream.tcc \
 /usr/include/c++/11/bits/istream.tcc \
 /usr/include/c++/11/bits/sstream.tcc /usr/include/c++/11/codecvt \
 /usr/include/c++/11/bits/fs_dir.h /usr/include/c++/11/bits/fs_ops.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qdatetime.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcalendar.h
