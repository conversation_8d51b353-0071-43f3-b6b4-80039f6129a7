
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/Desktop/Deepfake predictor/build/DeepfakePredictor_autogen/mocs_compilation.cpp" "CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.o" "gcc" "CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.o.d"
  "/home/<USER>/Desktop/Deepfake predictor/src/appwindow.cpp" "CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.o" "gcc" "CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.o.d"
  "/home/<USER>/Desktop/Deepfake predictor/src/deepfakedetector.cpp" "CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.o" "gcc" "CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.o.d"
  "/home/<USER>/Desktop/Deepfake predictor/src/main.cpp" "CMakeFiles/DeepfakePredictor.dir/src/main.cpp.o" "gcc" "CMakeFiles/DeepfakePredictor.dir/src/main.cpp.o.d"
  "/home/<USER>/Desktop/Deepfake predictor/src/reportgenerator.cpp" "CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.o" "gcc" "CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.o.d"
  "/home/<USER>/Desktop/Deepfake predictor/src/visualizer.cpp" "CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.o" "gcc" "CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
