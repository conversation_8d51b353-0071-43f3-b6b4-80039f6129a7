# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = "/home/<USER>/Desktop/Deepfake predictor"

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = "/home/<USER>/Desktop/Deepfake predictor/build"

# Include any dependencies generated for this target.
include CMakeFiles/DeepfakePredictor.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/DeepfakePredictor.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/DeepfakePredictor.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/DeepfakePredictor.dir/flags.make

CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.o: CMakeFiles/DeepfakePredictor.dir/flags.make
CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.o: DeepfakePredictor_autogen/mocs_compilation.cpp
CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.o: CMakeFiles/DeepfakePredictor.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir="/home/<USER>/Desktop/Deepfake predictor/build/CMakeFiles" --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.o -MF CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.o -c "/home/<USER>/Desktop/Deepfake predictor/build/DeepfakePredictor_autogen/mocs_compilation.cpp"

CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E "/home/<USER>/Desktop/Deepfake predictor/build/DeepfakePredictor_autogen/mocs_compilation.cpp" > CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.i

CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S "/home/<USER>/Desktop/Deepfake predictor/build/DeepfakePredictor_autogen/mocs_compilation.cpp" -o CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.s

CMakeFiles/DeepfakePredictor.dir/src/main.cpp.o: CMakeFiles/DeepfakePredictor.dir/flags.make
CMakeFiles/DeepfakePredictor.dir/src/main.cpp.o: ../src/main.cpp
CMakeFiles/DeepfakePredictor.dir/src/main.cpp.o: CMakeFiles/DeepfakePredictor.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir="/home/<USER>/Desktop/Deepfake predictor/build/CMakeFiles" --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/DeepfakePredictor.dir/src/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DeepfakePredictor.dir/src/main.cpp.o -MF CMakeFiles/DeepfakePredictor.dir/src/main.cpp.o.d -o CMakeFiles/DeepfakePredictor.dir/src/main.cpp.o -c "/home/<USER>/Desktop/Deepfake predictor/src/main.cpp"

CMakeFiles/DeepfakePredictor.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/DeepfakePredictor.dir/src/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E "/home/<USER>/Desktop/Deepfake predictor/src/main.cpp" > CMakeFiles/DeepfakePredictor.dir/src/main.cpp.i

CMakeFiles/DeepfakePredictor.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/DeepfakePredictor.dir/src/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S "/home/<USER>/Desktop/Deepfake predictor/src/main.cpp" -o CMakeFiles/DeepfakePredictor.dir/src/main.cpp.s

CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.o: CMakeFiles/DeepfakePredictor.dir/flags.make
CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.o: ../src/appwindow.cpp
CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.o: CMakeFiles/DeepfakePredictor.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir="/home/<USER>/Desktop/Deepfake predictor/build/CMakeFiles" --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.o -MF CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.o.d -o CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.o -c "/home/<USER>/Desktop/Deepfake predictor/src/appwindow.cpp"

CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E "/home/<USER>/Desktop/Deepfake predictor/src/appwindow.cpp" > CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.i

CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S "/home/<USER>/Desktop/Deepfake predictor/src/appwindow.cpp" -o CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.s

CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.o: CMakeFiles/DeepfakePredictor.dir/flags.make
CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.o: ../src/deepfakedetector.cpp
CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.o: CMakeFiles/DeepfakePredictor.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir="/home/<USER>/Desktop/Deepfake predictor/build/CMakeFiles" --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.o -MF CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.o.d -o CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.o -c "/home/<USER>/Desktop/Deepfake predictor/src/deepfakedetector.cpp"

CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E "/home/<USER>/Desktop/Deepfake predictor/src/deepfakedetector.cpp" > CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.i

CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S "/home/<USER>/Desktop/Deepfake predictor/src/deepfakedetector.cpp" -o CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.s

CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.o: CMakeFiles/DeepfakePredictor.dir/flags.make
CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.o: ../src/visualizer.cpp
CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.o: CMakeFiles/DeepfakePredictor.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir="/home/<USER>/Desktop/Deepfake predictor/build/CMakeFiles" --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.o -MF CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.o.d -o CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.o -c "/home/<USER>/Desktop/Deepfake predictor/src/visualizer.cpp"

CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E "/home/<USER>/Desktop/Deepfake predictor/src/visualizer.cpp" > CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.i

CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S "/home/<USER>/Desktop/Deepfake predictor/src/visualizer.cpp" -o CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.s

CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.o: CMakeFiles/DeepfakePredictor.dir/flags.make
CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.o: ../src/reportgenerator.cpp
CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.o: CMakeFiles/DeepfakePredictor.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir="/home/<USER>/Desktop/Deepfake predictor/build/CMakeFiles" --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.o -MF CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.o.d -o CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.o -c "/home/<USER>/Desktop/Deepfake predictor/src/reportgenerator.cpp"

CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E "/home/<USER>/Desktop/Deepfake predictor/src/reportgenerator.cpp" > CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.i

CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S "/home/<USER>/Desktop/Deepfake predictor/src/reportgenerator.cpp" -o CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.s

# Object files for target DeepfakePredictor
DeepfakePredictor_OBJECTS = \
"CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.o" \
"CMakeFiles/DeepfakePredictor.dir/src/main.cpp.o" \
"CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.o" \
"CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.o" \
"CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.o" \
"CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.o"

# External object files for target DeepfakePredictor
DeepfakePredictor_EXTERNAL_OBJECTS =

DeepfakePredictor: CMakeFiles/DeepfakePredictor.dir/DeepfakePredictor_autogen/mocs_compilation.cpp.o
DeepfakePredictor: CMakeFiles/DeepfakePredictor.dir/src/main.cpp.o
DeepfakePredictor: CMakeFiles/DeepfakePredictor.dir/src/appwindow.cpp.o
DeepfakePredictor: CMakeFiles/DeepfakePredictor.dir/src/deepfakedetector.cpp.o
DeepfakePredictor: CMakeFiles/DeepfakePredictor.dir/src/visualizer.cpp.o
DeepfakePredictor: CMakeFiles/DeepfakePredictor.dir/src/reportgenerator.cpp.o
DeepfakePredictor: CMakeFiles/DeepfakePredictor.dir/build.make
DeepfakePredictor: /usr/lib/x86_64-linux-gnu/libQt6PrintSupport.so.6.2.4
DeepfakePredictor: /usr/lib/x86_64-linux-gnu/libQt6Widgets.so.6.2.4
DeepfakePredictor: /usr/lib/x86_64-linux-gnu/libQt6Gui.so.6.2.4
DeepfakePredictor: /usr/lib/x86_64-linux-gnu/libQt6Core.so.6.2.4
DeepfakePredictor: /usr/lib/x86_64-linux-gnu/libGLX.so
DeepfakePredictor: /usr/lib/x86_64-linux-gnu/libOpenGL.so
DeepfakePredictor: CMakeFiles/DeepfakePredictor.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir="/home/<USER>/Desktop/Deepfake predictor/build/CMakeFiles" --progress-num=$(CMAKE_PROGRESS_7) "Linking CXX executable DeepfakePredictor"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/DeepfakePredictor.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/DeepfakePredictor.dir/build: DeepfakePredictor
.PHONY : CMakeFiles/DeepfakePredictor.dir/build

CMakeFiles/DeepfakePredictor.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/DeepfakePredictor.dir/cmake_clean.cmake
.PHONY : CMakeFiles/DeepfakePredictor.dir/clean

CMakeFiles/DeepfakePredictor.dir/depend:
	cd "/home/<USER>/Desktop/Deepfake predictor/build" && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" "/home/<USER>/Desktop/Deepfake predictor" "/home/<USER>/Desktop/Deepfake predictor" "/home/<USER>/Desktop/Deepfake predictor/build" "/home/<USER>/Desktop/Deepfake predictor/build" "/home/<USER>/Desktop/Deepfake predictor/build/CMakeFiles/DeepfakePredictor.dir/DependInfo.cmake" --color=$(COLOR)
.PHONY : CMakeFiles/DeepfakePredictor.dir/depend

